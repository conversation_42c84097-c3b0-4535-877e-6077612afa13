#!/usr/bin/env node

/**
 * 修复重复导出声明的脚本
 */

const fs = require('fs');
const path = require('path');

// 需要修复的模式
const FIXES = [
  {
    // 修复 export const BearJiaIcon = BearJiaIcon; 这种自引用
    pattern: /export const (BearJiaIcon|BearJiaUtil) = \1;/g,
    replacement: (match, name) => {
      const oldName = name.replace('BearJ<PERSON>', 'Autoee');
      return `export const ${oldName} = ${name};`;
    },
    description: '修复自引用导出声明'
  }
];

/**
 * 处理单个文件
 */
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    
    // 应用所有修复规则
    for (const fix of FIXES) {
      const newContent = content.replace(fix.pattern, fix.replacement);
      if (newContent !== content) {
        content = newContent;
        hasChanges = true;
        console.log(`✅ ${fix.description}: ${filePath}`);
      }
    }
    
    // 如果有变化，写回文件
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 处理文件失败: ${filePath} - ${error.message}`);
    return false;
  }
}

/**
 * 查找需要处理的文件
 */
function findTargetFiles() {
  const targetFiles = [
    'src/utils/BearJiaIcon.js',
    'src/utils/BearJiaUtil.js'
  ];
  
  return targetFiles.filter(file => fs.existsSync(file));
}

/**
 * 主函数
 */
function main() {
  console.log('🔧 开始修复重复导出声明...\n');
  
  const files = findTargetFiles();
  
  if (files.length === 0) {
    console.log('没有找到需要修复的文件');
    return;
  }
  
  console.log(`找到 ${files.length} 个文件需要检查\n`);
  
  let fixedCount = 0;
  
  for (const file of files) {
    if (processFile(file)) {
      fixedCount++;
    }
  }
  
  if (fixedCount > 0) {
    console.log(`\n🎉 修复完成! 共修复了 ${fixedCount} 个文件`);
    console.log('\n📝 建议运行以下命令验证修复:');
    console.log('npm run dev');
  } else {
    console.log('\n✅ 没有发现需要修复的问题');
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  processFile,
  findTargetFiles,
  FIXES
};

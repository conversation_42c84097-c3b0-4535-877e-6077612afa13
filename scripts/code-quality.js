#!/usr/bin/env node

/**
 * 代码质量检查脚本
 */

const fs = require('fs');
const path = require('path');

// 代码质量检查规则
const QUALITY_RULES = [
  {
    name: '检查console.log',
    pattern: /console\.log\(/g,
    severity: 'warning',
    message: '发现console.log，建议在生产环境中移除'
  },
  {
    name: '检查debugger',
    pattern: /debugger/g,
    severity: 'error',
    message: '发现debugger语句，必须在生产环境中移除'
  },
  {
    name: '检查TODO注释',
    pattern: /\/\/\s*TODO|\/\*\s*TODO/gi,
    severity: 'info',
    message: '发现TODO注释，建议及时处理'
  },
  {
    name: '检查FIXME注释',
    pattern: /\/\/\s*FIXME|\/\*\s*FIXME/gi,
    severity: 'warning',
    message: '发现FIXME注释，需要修复'
  },
  {
    name: '检查硬编码URL',
    pattern: /https?:\/\/[^\s'"]+/g,
    severity: 'warning',
    message: '发现硬编码URL，建议使用配置文件'
  },
  {
    name: '检查空的catch块',
    pattern: /catch\s*\([^)]*\)\s*\{\s*\}/g,
    severity: 'error',
    message: '发现空的catch块，应该处理异常'
  },
  {
    name: '检查长函数',
    pattern: /function[^{]*\{[\s\S]*?\}/g,
    severity: 'warning',
    message: '函数过长，建议拆分',
    customCheck: (match) => {
      const lines = match.split('\n').length;
      return lines > 50;
    }
  }
];

// 文件类型配置
const FILE_TYPES = {
  javascript: ['.js', '.ts'],
  vue: ['.vue'],
  style: ['.css', '.less', '.scss'],
  all: ['.js', '.ts', '.vue', '.css', '.less', '.scss']
};

/**
 * 递归查找文件
 */
function findFiles(dir, extensions, files = []) {
  if (!fs.existsSync(dir)) return files;
  
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      if (item !== 'node_modules' && item !== '.git' && item !== 'dist') {
        findFiles(fullPath, extensions, files);
      }
    } else {
      const ext = path.extname(item);
      if (extensions.includes(ext)) {
        files.push(fullPath);
      }
    }
  }
  
  return files;
}

/**
 * 检查单个文件
 */
function checkFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];
    
    for (const rule of QUALITY_RULES) {
      const matches = content.match(rule.pattern);
      if (matches) {
        for (const match of matches) {
          // 如果有自定义检查函数，使用它
          if (rule.customCheck && !rule.customCheck(match)) {
            continue;
          }
          
          // 找到匹配的行号
          const lines = content.split('\n');
          let lineNumber = 1;
          for (const line of lines) {
            if (line.includes(match)) {
              issues.push({
                rule: rule.name,
                severity: rule.severity,
                message: rule.message,
                line: lineNumber,
                content: line.trim()
              });
              break;
            }
            lineNumber++;
          }
        }
      }
    }
    
    return issues;
  } catch (error) {
    console.error(`检查文件失败: ${filePath} - ${error.message}`);
    return [];
  }
}

/**
 * 检查代码复杂度
 */
function checkComplexity(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];
    
    // 检查圈复杂度（简单版本）
    const complexityPatterns = [
      /if\s*\(/g,
      /else\s+if\s*\(/g,
      /while\s*\(/g,
      /for\s*\(/g,
      /switch\s*\(/g,
      /case\s+/g,
      /catch\s*\(/g,
      /&&/g,
      /\|\|/g
    ];
    
    let complexity = 1; // 基础复杂度
    for (const pattern of complexityPatterns) {
      const matches = content.match(pattern);
      if (matches) {
        complexity += matches.length;
      }
    }
    
    if (complexity > 10) {
      issues.push({
        rule: '圈复杂度检查',
        severity: 'warning',
        message: `文件复杂度过高 (${complexity})，建议重构`,
        line: 1,
        content: '整个文件'
      });
    }
    
    return issues;
  } catch (error) {
    return [];
  }
}

/**
 * 检查重复代码
 */
function checkDuplication(files) {
  const duplicates = [];
  const codeBlocks = new Map();
  
  for (const file of files) {
    try {
      const content = fs.readFileSync(file, 'utf8');
      const lines = content.split('\n');
      
      // 检查连续的5行代码
      for (let i = 0; i <= lines.length - 5; i++) {
        const block = lines.slice(i, i + 5)
          .map(line => line.trim())
          .filter(line => line && !line.startsWith('//') && !line.startsWith('/*'))
          .join('\n');
        
        if (block.length > 50) { // 只检查有意义的代码块
          if (codeBlocks.has(block)) {
            duplicates.push({
              block,
              files: [codeBlocks.get(block), { file, startLine: i + 1 }]
            });
          } else {
            codeBlocks.set(block, { file, startLine: i + 1 });
          }
        }
      }
    } catch (error) {
      // 忽略读取错误
    }
  }
  
  return duplicates;
}

/**
 * 生成报告
 */
function generateReport(results, duplicates) {
  console.log('\n📊 代码质量检查报告\n');
  
  let totalIssues = 0;
  let errorCount = 0;
  let warningCount = 0;
  let infoCount = 0;
  
  // 按文件分组显示问题
  const fileGroups = {};
  for (const result of results) {
    if (result.issues.length > 0) {
      fileGroups[result.file] = result.issues;
      totalIssues += result.issues.length;
      
      for (const issue of result.issues) {
        switch (issue.severity) {
          case 'error': errorCount++; break;
          case 'warning': warningCount++; break;
          case 'info': infoCount++; break;
        }
      }
    }
  }
  
  // 显示统计信息
  console.log(`📈 统计信息:`);
  console.log(`   总问题数: ${totalIssues}`);
  console.log(`   错误: ${errorCount}`);
  console.log(`   警告: ${warningCount}`);
  console.log(`   信息: ${infoCount}`);
  console.log(`   检查文件数: ${results.length}`);
  console.log(`   有问题的文件数: ${Object.keys(fileGroups).length}\n`);
  
  // 显示详细问题
  if (totalIssues > 0) {
    console.log('🔍 详细问题:\n');
    for (const [file, issues] of Object.entries(fileGroups)) {
      console.log(`📄 ${file}:`);
      for (const issue of issues) {
        const icon = issue.severity === 'error' ? '❌' : 
                    issue.severity === 'warning' ? '⚠️' : 'ℹ️';
        console.log(`   ${icon} 第${issue.line}行: ${issue.message}`);
        console.log(`      ${issue.content}`);
      }
      console.log('');
    }
  }
  
  // 显示重复代码
  if (duplicates.length > 0) {
    console.log('🔄 重复代码:\n');
    for (const duplicate of duplicates.slice(0, 5)) { // 只显示前5个
      console.log(`重复代码块:`);
      for (const location of duplicate.files) {
        console.log(`   📄 ${location.file}:${location.startLine}`);
      }
      console.log('');
    }
  }
  
  // 给出建议
  console.log('💡 优化建议:\n');
  if (errorCount > 0) {
    console.log('   🔴 请优先修复所有错误级别的问题');
  }
  if (warningCount > 0) {
    console.log('   🟡 建议修复警告级别的问题以提高代码质量');
  }
  if (duplicates.length > 0) {
    console.log('   🔄 考虑重构重复代码，提取公共函数或组件');
  }
  if (totalIssues === 0) {
    console.log('   ✅ 代码质量良好，没有发现明显问题');
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始代码质量检查...\n');
  
  const files = findFiles('./src', FILE_TYPES.all);
  console.log(`找到 ${files.length} 个文件需要检查\n`);
  
  const results = [];
  
  for (const file of files) {
    const issues = [
      ...checkFile(file),
      ...checkComplexity(file)
    ];
    
    results.push({
      file,
      issues
    });
  }
  
  // 检查重复代码
  const duplicates = checkDuplication(files);
  
  // 生成报告
  generateReport(results, duplicates);
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  checkFile,
  checkComplexity,
  checkDuplication,
  findFiles
};

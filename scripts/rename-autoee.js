#!/usr/bin/env node

/**
 * 批量重命名Autoee为BearJia的脚本
 */

const fs = require('fs');
const path = require('path');

// 需要处理的文件扩展名
const FILE_EXTENSIONS = ['.vue', '.js', '.ts'];

// 需要替换的模式
const REPLACEMENTS = [
  {
    from: /import\s+AutoeeUtil\s+from\s+['"]@\/utils\/AutoeeUtil\.js['"]/g,
    to: "import BearJiaUtil from '@/utils/BearJiaUtil.js'"
  },
  {
    from: /import\s*{\s*AutoeeUtil\s*}\s*from\s+['"]@\/utils\/AutoeeUtil\.js['"]/g,
    to: "import { BearJiaUtil } from '@/utils/BearJiaUtil.js'"
  },
  {
    from: /import\s*{\s*AutoeeIcon\s*}\s*from\s+['"]@\/utils\/AutoeeIcon\.js['"]/g,
    to: "import { BearJiaIcon } from '@/utils/BearJiaIcon.js'"
  },
  {
    from: /AutoeeUtil\./g,
    to: 'BearJiaUtil.'
  },
  {
    from: /(?<!export const )AutoeeIcon/g,
    to: 'BearJiaIcon'
  }
];

/**
 * 递归查找文件
 */
function findFiles(dir, files = []) {
  if (!fs.existsSync(dir)) return files;

  const items = fs.readdirSync(dir);

  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory()) {
      // 跳过 node_modules 和 .git 目录
      if (item !== 'node_modules' && item !== '.git' && item !== 'dist') {
        findFiles(fullPath, files);
      }
    } else {
      // 检查文件扩展名
      const ext = path.extname(item);
      if (FILE_EXTENSIONS.includes(ext)) {
        files.push(fullPath);
      }
    }
  }

  return files;
}

/**
 * 处理单个文件
 */
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;

    // 应用所有替换规则
    for (const replacement of REPLACEMENTS) {
      const newContent = content.replace(replacement.from, replacement.to);
      if (newContent !== content) {
        content = newContent;
        hasChanges = true;
      }
    }

    // 如果有变化，写回文件
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已更新: ${filePath}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ 处理文件失败: ${filePath} - ${error.message}`);
    return false;
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始批量重命名 Autoee 为 BearJia...\n');

  const files = findFiles('./src');
  console.log(`找到 ${files.length} 个文件需要检查\n`);

  let processedCount = 0;

  for (const file of files) {
    if (processFile(file)) {
      processedCount++;
    }
  }

  console.log(`\n🎉 处理完成! 共更新了 ${processedCount} 个文件`);

  if (processedCount > 0) {
    console.log('\n📝 建议运行以下命令验证更改:');
    console.log('npm run dev');
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  processFile,
  findFiles,
  REPLACEMENTS
};

#!/usr/bin/env node

/**
 * 项目清理脚本
 * 用于清理项目中的临时文件、备份文件等
 */

const fs = require('fs');
const path = require('path');

// 需要清理的文件模式
const CLEANUP_PATTERNS = [
  /.*~$/,           // 备份文件
  /.*\.bak$/,       // 备份文件
  /.*\.tmp$/,       // 临时文件
  /.*\.temp$/,      // 临时文件
  /.*\.swp$/,       // Vim交换文件
  /.*\.swo$/,       // Vim交换文件
  /\.DS_Store$/,    // macOS系统文件
  /Thumbs\.db$/,    // Windows缩略图文件
];

// 需要清理的目录
const CLEANUP_DIRS = [
  'node_modules/.cache',
  'dist',
  '.nuxt',
  '.next',
  'coverage',
];

/**
 * 递归查找匹配的文件
 */
function findFilesToCleanup(dir, files = []) {
  if (!fs.existsSync(dir)) return files;
  
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // 跳过 node_modules 和 .git 目录
      if (item !== 'node_modules' && item !== '.git') {
        findFilesToCleanup(fullPath, files);
      }
    } else {
      // 检查文件是否匹配清理模式
      for (const pattern of CLEANUP_PATTERNS) {
        if (pattern.test(item)) {
          files.push(fullPath);
          break;
        }
      }
    }
  }
  
  return files;
}

/**
 * 清理文件
 */
function cleanupFiles() {
  console.log('🧹 开始清理项目文件...\n');
  
  const filesToCleanup = findFilesToCleanup('./src');
  
  if (filesToCleanup.length === 0) {
    console.log('✅ 没有找到需要清理的文件');
    return;
  }
  
  console.log(`找到 ${filesToCleanup.length} 个文件需要清理:`);
  filesToCleanup.forEach(file => console.log(`  - ${file}`));
  
  console.log('\n开始清理...');
  
  let cleanedCount = 0;
  for (const file of filesToCleanup) {
    try {
      fs.unlinkSync(file);
      console.log(`✅ 已删除: ${file}`);
      cleanedCount++;
    } catch (error) {
      console.log(`❌ 删除失败: ${file} - ${error.message}`);
    }
  }
  
  console.log(`\n🎉 清理完成! 共清理了 ${cleanedCount} 个文件`);
}

/**
 * 清理目录
 */
function cleanupDirectories() {
  console.log('\n🗂️  检查需要清理的目录...\n');
  
  for (const dir of CLEANUP_DIRS) {
    if (fs.existsSync(dir)) {
      try {
        fs.rmSync(dir, { recursive: true, force: true });
        console.log(`✅ 已清理目录: ${dir}`);
      } catch (error) {
        console.log(`❌ 清理目录失败: ${dir} - ${error.message}`);
      }
    }
  }
}

/**
 * 检查重复文件
 */
function checkDuplicateFiles() {
  console.log('\n🔍 检查重复文件...\n');
  
  // 常见的重复文件模式
  const duplicatePatterns = [
    { pattern: /login\.(js|ts)$/, dir: 'src/api' },
    { pattern: /global\.less$/, dir: 'src' },
    { pattern: /menu\.(js|ts)$/, dir: 'src/api' },
  ];
  
  for (const { pattern, dir } of duplicatePatterns) {
    const files = findFilesToCleanup(dir).filter(file => pattern.test(path.basename(file)));
    if (files.length > 1) {
      console.log(`⚠️  发现重复文件 (${pattern}):`);
      files.forEach(file => console.log(`  - ${file}`));
      console.log('');
    }
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 BearJia 项目清理工具\n');
  
  cleanupFiles();
  cleanupDirectories();
  checkDuplicateFiles();
  
  console.log('\n✨ 所有清理任务完成!');
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  cleanupFiles,
  cleanupDirectories,
  checkDuplicateFiles,
  findFilesToCleanup,
};

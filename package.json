{"name": "bear<PERSON>a-ui", "version": "0.0.0", "scripts": {"dev": "vite --mode dev", "build": "vite build", "build:prod": "vite build --mode prod", "build:test": "vite build --mode test", "preview": "vite preview", "cleanup": "node scripts/cleanup.js", "rename-autoee": "node scripts/rename-autoee.js", "fix-exports": "node scripts/fix-duplicate-exports.js", "lint": "eslint src --ext .vue,.js,.ts", "lint:fix": "eslint src --ext .vue,.js,.ts --fix"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@vueuse/core": "^10.9.0", "ant-design-vue": "^4.1.2", "axios": "^1.6.7", "core-js": "^3.36.0", "dayjs": "^1.11.10", "echarts": "^5.6.0", "file-saver": "^2.0.5", "js-cookie": "^3.0.5", "nprogress": "^0.2.0", "particles.js": "^2.0.0", "pinia": "^2.1.7", "vue": "^3.4.21", "vue-request": "^2.0.4", "vue-router": "^4.3.0", "vue3-highlightjs": "^1.0.5"}, "devDependencies": {"@types/nprogress": "^0.2.3", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/compiler-sfc": "^3.4.21", "@vue/eslint-config-prettier": "^9.0.0", "less": "^4.2.0", "vite": "^5.1.4"}}
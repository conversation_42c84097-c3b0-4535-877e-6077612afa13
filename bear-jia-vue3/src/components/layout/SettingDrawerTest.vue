<template>
  <div class="setting-drawer-test">
    <a-button type="primary" @click="showDrawer = true">
      打开设置面板
    </a-button>
    
    <SettingDrawer
      v-model:visible="showDrawer"
      :settings="settings"
      @update:settings="handleSettingsUpdate"
    />
    
    <div class="test-info">
      <h3>当前设置状态：</h3>
      <pre>{{ JSON.stringify(settings, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import SettingDrawer from './SettingDrawer.vue';

const showDrawer = ref(false);

const settings = ref({
  darkMode: false,
  primaryColor: '#1890ff',
  layout: 'side',
  fixedHeader: true,
  fixedSidebar: false,
  showBreadcrumb: true,
  showTabs: true,
  showFooter: true,
  contentWidth: 'fluid',
  colorWeak: false
});

const handleSettingsUpdate = (newSettings) => {
  console.log('Settings updated:', newSettings);
  settings.value = { ...newSettings };
};
</script>

<style scoped>
.setting-drawer-test {
  padding: 20px;
}

.test-info {
  margin-top: 20px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 4px;
}

.test-info pre {
  background: white;
  padding: 12px;
  border-radius: 4px;
  overflow: auto;
}
</style>

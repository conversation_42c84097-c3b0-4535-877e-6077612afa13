/**
 * 全局配置初始化工具
 * 用于在应用启动时初始化各种配置
 */

import { useAppStore } from '@/stores/app';
import { useTableConfigStore } from '@/stores/tableConfig';

/**
 * 初始化应用配置
 */
export function initAppConfig() {
  const appStore = useAppStore();
  
  // 应用主题设置
  appStore.applyTheme();
  
  // 设置系统标题
  if (appStore.systemConfig.title) {
    document.title = appStore.systemConfig.title;
  }
  
  // 设置meta信息
  updateMetaInfo(appStore.systemConfig);
  
  console.log('✅ 应用配置初始化完成');
}

/**
 * 初始化表格配置
 */
export function initTableConfig() {
  const tableConfigStore = useTableConfigStore();
  
  // 从localStorage加载表格配置
  tableConfigStore.loadConfig();
  
  console.log('✅ 表格配置初始化完成');
}

/**
 * 更新页面meta信息
 */
function updateMetaInfo(systemConfig) {
  // 更新description
  if (systemConfig.description) {
    updateMetaTag('description', systemConfig.description);
  }
  
  // 更新keywords
  if (systemConfig.keywords) {
    updateMetaTag('keywords', systemConfig.keywords);
  }
  
  // 更新author
  updateMetaTag('author', 'BearJia Team');
  
  // 更新viewport
  updateMetaTag('viewport', 'width=device-width, initial-scale=1.0');
}

/**
 * 更新或创建meta标签
 */
function updateMetaTag(name, content) {
  let meta = document.querySelector(`meta[name="${name}"]`);
  
  if (!meta) {
    meta = document.createElement('meta');
    meta.name = name;
    document.head.appendChild(meta);
  }
  
  meta.content = content;
}

/**
 * 初始化CSS变量
 */
export function initCSSVariables() {
  const appStore = useAppStore();
  const root = document.documentElement;
  
  // 设置主题色相关变量
  root.style.setProperty('--primary-color', appStore.layoutSettings.primaryColor);
  root.style.setProperty('--ant-primary-color', appStore.layoutSettings.primaryColor);
  
  // 计算主题色的衍生颜色
  const primaryColor = appStore.layoutSettings.primaryColor;
  const primaryColorRgb = hexToRgb(primaryColor);
  
  if (primaryColorRgb) {
    // 设置主题色的透明度变体
    root.style.setProperty('--ant-primary-1', `rgba(${primaryColorRgb.r}, ${primaryColorRgb.g}, ${primaryColorRgb.b}, 0.1)`);
    root.style.setProperty('--ant-primary-2', `rgba(${primaryColorRgb.r}, ${primaryColorRgb.g}, ${primaryColorRgb.b}, 0.2)`);
    root.style.setProperty('--ant-primary-3', `rgba(${primaryColorRgb.r}, ${primaryColorRgb.g}, ${primaryColorRgb.b}, 0.3)`);
    
    // 设置hover和active状态的颜色
    root.style.setProperty('--ant-primary-color-hover', lightenColor(primaryColor, 10));
    root.style.setProperty('--ant-primary-color-active', darkenColor(primaryColor, 10));
  }
  
  console.log('✅ CSS变量初始化完成');
}

/**
 * 将十六进制颜色转换为RGB
 */
function hexToRgb(hex) {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

/**
 * 使颜色变亮
 */
function lightenColor(color, percent) {
  const rgb = hexToRgb(color);
  if (!rgb) return color;
  
  const factor = 1 + percent / 100;
  const r = Math.min(255, Math.round(rgb.r * factor));
  const g = Math.min(255, Math.round(rgb.g * factor));
  const b = Math.min(255, Math.round(rgb.b * factor));
  
  return `rgb(${r}, ${g}, ${b})`;
}

/**
 * 使颜色变暗
 */
function darkenColor(color, percent) {
  const rgb = hexToRgb(color);
  if (!rgb) return color;
  
  const factor = 1 - percent / 100;
  const r = Math.max(0, Math.round(rgb.r * factor));
  const g = Math.max(0, Math.round(rgb.g * factor));
  const b = Math.max(0, Math.round(rgb.b * factor));
  
  return `rgb(${r}, ${g}, ${b})`;
}

/**
 * 初始化色弱模式
 */
export function initColorWeakMode() {
  const appStore = useAppStore();
  
  if (appStore.layoutSettings.colorWeak) {
    document.documentElement.classList.add('color-weak');
    
    // 添加色弱模式的CSS
    const style = document.createElement('style');
    style.textContent = `
      .color-weak {
        filter: invert(80%);
      }
      .color-weak .ant-layout-header,
      .color-weak .ant-layout-footer,
      .color-weak .ant-layout-sider {
        filter: invert(100%);
      }
    `;
    document.head.appendChild(style);
  }
  
  console.log('✅ 色弱模式初始化完成');
}

/**
 * 初始化所有配置
 */
export function initAllConfigs() {
  try {
    initAppConfig();
    initTableConfig();
    initCSSVariables();
    initColorWeakMode();
    
    console.log('🎉 所有配置初始化完成');
  } catch (error) {
    console.error('❌ 配置初始化失败:', error);
  }
}

/**
 * 监听配置变化
 */
export function watchConfigChanges() {
  const appStore = useAppStore();
  
  // 监听主题变化
  appStore.$subscribe((mutation, state) => {
    if (mutation.type === 'direct' && mutation.events) {
      const changedKeys = mutation.events.map(event => event.key);
      
      // 如果主题相关配置发生变化，重新应用主题
      if (changedKeys.some(key => ['primaryColor', 'darkMode', 'colorWeak'].includes(key))) {
        appStore.applyTheme();
        initCSSVariables();
      }
      
      // 如果系统配置发生变化，更新页面信息
      if (changedKeys.some(key => key.startsWith('system'))) {
        updateMetaInfo(state.systemConfig);
        if (state.systemConfig.title) {
          document.title = state.systemConfig.title;
        }
      }
    }
  });
  
  console.log('✅ 配置变化监听已启动');
}

export default {
  initAppConfig,
  initTableConfig,
  initCSSVariables,
  initColorWeakMode,
  initAllConfigs,
  watchConfigChanges
};

<template>
  <a-tag :color="tagColor">
    {{ tagText }}
  </a-tag>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  options: {
    type: Array,
    default: () => []
  },
  value: {
    type: [String, Number],
    default: ''
  }
});

// 计算标签文本
const tagText = computed(() => {
  const option = props.options.find(item => item.value === String(props.value));
  return option ? option.label : props.value;
});

// 计算标签颜色
const tagColor = computed(() => {
  const value = String(props.value);
  switch (value) {
    case '0':
      return 'green'; // 正常/启用
    case '1':
      return 'red';   // 禁用/停用
    case '2':
      return 'orange'; // 待审核
    default:
      return 'blue';
  }
});
</script>

<style scoped>
/* 可以添加自定义样式 */
</style>

<template>
  <div>
    <div id="editor" class="editor" ref="editorRef"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import { useAppStore } from '@/stores/app';
import { message } from 'ant-design-vue';
import storage from 'store';
import { ACCESS_TOKEN } from '@/store/mutation-types';
import E from 'wangeditor';
import hljs from 'highlight.js';

// 定义 Props
const props = defineProps({
  value: {
    type: String,
    default: '',
  },
  height: {
    type: Number,
    default: null,
  },
  readOnly: {
    type: Boolean,
    default: false,
  },
  fileSize: {
    type: Number,
    default: 1024,
  },
  imageSize: {
    type: Number,
    default: 5,
  },
  videoSize: {
    type: Number,
    default: 1024,
  },
});

// 定义 Emits
const emit = defineEmits(['input']);

// 响应式数据
const editorRef = ref(null);
const editor = ref(null);
const currentValue = ref(props.value || '');
const uploadImgUrl = ref(`${import.meta.env.VUE_APP_BASE_API}/common/upload`);
const headers = reactive({
  Authorization: 'Bearer ' + storage.get(ACCESS_TOKEN),
  Accept: 'application/json, text/plain, */*',
});

// 获取 store
const appStore = useAppStore();

// 替换 Vuex store 的使用
const app = computed(() => appStore);

// 监听 value 变化
watch(
    () => props.value,
    (val) => {
      if (val !== currentValue.value) {
        currentValue.value = val === null ? '' : val;
        if (editor.value) {
          editor.value.txt.html(currentValue.value);
        }
      }
    },
    { immediate: true }
);

// 初始化编辑器
const init = () => {
  editor.value = new E(editorRef.value);
  // 代码高亮
  editor.value.highlight = hljs;

  // 高度设置
  if (props.height) {
    editor.value.config.height = props.height;
  }

  // z-index
  editor.value.config.zIndex = 0;

  // 自定义提示信息
  editor.value.config.customAlert = (s, t) => {
    switch (t) {
      case 'success':
        message.success(s);
        break;
      case 'info':
        message.info(s);
        break;
      case 'warning':
        message.warning(s);
        break;
      case 'error':
        message.error(s);
        break;
      default:
        message.info(s);
        break;
    }
  };

  // 图片上传配置
  editor.value.config.uploadImgMaxSize = 1024 * 1024 * props.imageSize;
  editor.value.config.uploadImgServer = uploadImgUrl.value;
  editor.value.config.uploadImgHeaders = headers;
  editor.value.config.uploadFileName = 'file';
  editor.value.config.uploadImgHooks = {
    customInsert: (insertImgFn, result) => {
      insertImgFn(result.url);
    },
  };

  // 视频上传配置
  editor.value.config.uploadVideoMaxSize = 1024 * 1024 * props.videoSize;
  editor.value.config.uploadVideoServer = uploadImgUrl.value;
  editor.value.config.uploadVideoHeaders = headers;
  editor.value.config.uploadVideoName = 'file';
  editor.value.config.uploadVideoHooks = {
    customInsert: (insertVideoFn, result) => {
      insertVideoFn(result.url);
    },
  };

  // 数据双向绑定
  editor.value.config.onchange = (newHtml) => {
    currentValue.value = newHtml;
    emit('input', newHtml);
  };

  // 创建编辑器
  editor.value.create();
  editor.value.txt.html(currentValue.value);

  // 只读模式
  if (props.readOnly) {
    editor.value.disable();
  }
};

// 处理上传错误
const handleUploadError = () => {
  message.error('图片插入失败');
};

// 在组件挂载时初始化
onMounted(() => {
  init();
});
</script>

<style lang="less" scoped>
.editor {
  /* 可根据需要添加样式 */
}
</style>
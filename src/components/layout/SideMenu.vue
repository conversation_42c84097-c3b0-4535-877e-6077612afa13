<template>
  <a-layout-sider 
    :theme="layoutSettings.theme"
    :collapsed="collapsed" 
    :trigger="null" 
    collapsible
    :class="{ 'dark-theme': layoutSettings.theme === 'dark' }"
    @collapse="$emit('update:collapsed', $event)"
  >
    <!-- Logo区域 -->
    <div class="layout-logo">
      <!-- <img v-if="layoutSettings && layoutSettings.theme === 'dark'" src="/src/assets/styles/loginPage/autoee-logo-white-noBg.png" alt="logo" class="logo-img" /> -->
      <!-- <img v-else src="/src/assets/styles/loginPage/autoee-logo.png" alt="logo" class="logo-img" /> -->
      <span class="logo-title" v-show="!collapsed">BearJia管理系统</span>
    </div>
    
    <!-- 菜单区域 -->
    <a-menu 
      v-model:selectedKeys="selectedKeys"
      v-model:openKeys="openKeys"
      :theme="layoutSettings.theme"
      mode="inline"
      class="side-menu"
      @openChange="handleOpenChange"
    >
      <a-menu-item key="workbench" @click="handleMenuClick('HomePage', '/home', '主页', 'Workbench', 'workbench', '工作台')">
        <template #icon>
          <BearJiaIcon icon="HomeOutlined" />
        </template>
        工作台
      </a-menu-item>
      
      <!-- 动态菜单 -->
      <template v-for="(router, index) in menuData" :key="router.path + '-' + index">
        <a-sub-menu>
          <template #icon>
            <BearJiaIcon :icon="router.meta?.icon || 'MenuOutlined'" />
          </template>
          <template #title>{{ router.meta?.title }}</template>
          
          <template v-for="(children, childIndex) in router.children" :key="children.path + '-' + childIndex">
            <!-- 二级父菜单 -->
            <template v-if="children.children">
              <a-sub-menu :key="children.path + '-' + childIndex">
                <template #icon>
                  <BearJiaIcon :icon="children.meta?.icon || 'AppstoreOutlined'" />
                </template>
                <template #title>{{ children.meta?.title }}</template>
                <!-- 三级菜单 -->
                <template v-for="(threeLevelChildren, threeIndex) in children.children" :key="threeLevelChildren.path + '-' + threeIndex">
                  <a-menu-item
                    @click="handleThreeLevelMenuClick(router.path, children, threeLevelChildren)"
                  >
                    <template #icon>
                      <BearJiaIcon :icon="threeLevelChildren.meta?.icon || 'BarsOutlined'" />
                    </template>
                    {{ threeLevelChildren.meta?.title }}
                  </a-menu-item>
                </template>
              </a-sub-menu>
            </template>
            <!-- 二级菜单 -->
            <template v-else>
              <a-menu-item 
                :key="children.path + '-' + childIndex"
                @click="handleMenuClick(router.name, router.path, router.meta.title, children.name, children.path, children.meta.title, children.component)"
              >
                <template #icon>
                  <BearJiaIcon :icon="children.meta?.icon || 'BarsOutlined'" />
                </template>
                {{ children.meta?.title }}
              </a-menu-item>
            </template>
          </template>
        </a-sub-menu>
      </template>
    </a-menu>
  </a-layout-sider>
</template>

<script setup>
import { ref } from 'vue';
import { BearJiaIcon } from '@/utils/BearJiaIcon.js';
import { useRouter } from 'vue-router';

const router = useRouter();

const props = defineProps({
  collapsed: {
    type: Boolean,
    required: true
  },
  menuData: {
    type: Array,
    required: true
  },
  layoutSettings: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update:collapsed', 'menuSelect']);

// 菜单状态
const selectedKeys = ref([]);
const openKeys = ref([]);

// 处理父菜单展开/收起
const handleOpenChange = (keys) => {
  openKeys.value = keys;
};

// 处理菜单点击
const handleMenuClick = (fatherName, fatherPath, fatherTitle, name, path, title, component) => {
  try {
    if (path === 'workbench') {
      selectedKeys.value = ['workbench'];
      openKeys.value = [];
    } else {
      selectedKeys.value = [path];
      if (fatherPath && !openKeys.value.includes(fatherPath)) {
        openKeys.value = [...openKeys.value, fatherPath];
      }
    }
    emit('menuSelect', {
      fatherName,
      fatherPath,
      fatherTitle,
      name,
      path,
      title,
      component
    });
  } catch (error) {
    console.error('菜单点击错误:', error);
  }
};

// 处理三级菜单点击
const handleThreeLevelMenuClick = (greatFatherPath, father, menu) => {
  try {
    selectedKeys.value = [menu.path];
    const parentKeys = [greatFatherPath, father.path].filter(Boolean);
    openKeys.value = parentKeys;
    
    emit('menuSelect', {
      greatFatherPath,
      father,
      menu
    });
  } catch (error) {
    console.error('三级菜单点击错误:', error);
  }
};
</script>

<style lang="less">
@import '@/style/components/menu.less';

:deep(.ant-layout-sider) {
  margin: 16px 0 16px 16px;
  border-radius: 16px;
  overflow: hidden;
  background: #fff;

  &.dark-theme {
    background: #001529;
    .layout-logo {
      background-color: #001529;
    }
  }
  &:not(.dark-theme) {
    .layout-logo {
      background-color: #fff;
    }
  }
}

.layout-logo {
  display: flex;
  align-items: center;
  padding: 16px;
  .logo-img {
    height: 32px;
    margin-right: 8px;
  }
  .logo-title {
    color: v-bind('layoutSettings.theme === "dark" ? "#fff" : "#000"');
    font-size: 18px;
    font-weight: 600;
    white-space: nowrap;
    opacity: 1;
    transition: all 0.3s;
  }
}

.side-menu {
  height: calc(100% - 64px);
}
</style>
<template>
  <a-drawer
    title="布局设置"
    placement="right"
    :open="visible"
    @close="$emit('update:visible', false)"
    width="300"
    :closable="false"
    style="z-index: 999"
    class="theme-setting-drawer"
  >
    <div class="setting-drawer-content">
      <!-- 侧边栏风格设置 -->
      <div class="setting-drawer-block">
        <h3>主题模式</h3>
        <div class="setting-drawer-block-checbox">
          <div class="setting-drawer-block-checbox-item" @click="handleThemeChange('dark')">
            <a-tooltip title="暗色主题">
              <img src="@/assets/images/dark.svg" alt="dark">
              <check-outlined v-if="localSettings.darkMode" class="setting-drawer-theme-color-icon" />
            </a-tooltip>
          </div>
          <div class="setting-drawer-block-checbox-item" @click="handleThemeChange('light')">
            <a-tooltip title="亮色主题">
              <img src="@/assets/images/light.svg" alt="light">
              <check-outlined v-if="!localSettings.darkMode" class="setting-drawer-theme-color-icon" />
            </a-tooltip>
          </div>
        </div>
      </div>

      <a-divider />

      <!-- 主题色设置 -->
      <div class="setting-drawer-block">
        <h3>主题色</h3>
        <div class="setting-drawer-theme-color">
          <a-tooltip v-for="(item, index) in colorList" :key="index" :title="item.key">
            <div
              class="setting-drawer-theme-color-block"
              :style="{ backgroundColor: item.color }"
              @click="handleColorChange(item.color)"
            >
              <check-outlined v-if="localSettings.primaryColor === item.color" />
            </div>
          </a-tooltip>
        </div>
      </div>

      <a-divider />

      <!-- 导航模式 -->
      <div class="setting-drawer-block">
        <h3>导航模式</h3>
        <div class="setting-drawer-block-checbox">
          <div class="setting-drawer-block-checbox-item" @click="handleLayoutChange('side')">
            <a-tooltip title="侧边菜单布局">
              <img src="https://gw.alipayobjects.com/zos/rmsportal/JopDzEhOqwOjeNTXkoje.svg" alt="side">
              <check-outlined v-if="localSettings.layout === 'side'" class="setting-drawer-theme-color-icon" />
            </a-tooltip>
          </div>
          <div class="setting-drawer-block-checbox-item" @click="handleLayoutChange('top')">
            <a-tooltip title="顶部菜单布局">
              <img src="https://gw.alipayobjects.com/zos/rmsportal/KDNDBbriJhLwuqMoxcAr.svg" alt="top">
              <check-outlined v-if="localSettings.layout === 'top'" class="setting-drawer-theme-color-icon" />
            </a-tooltip>
          </div>
        </div>



        <a-divider />

        <!-- 布局设置 -->
        <a-list :split="false">
          <a-list-item>
            <template #extra>
              <a-select
                v-model:value="localSettings.contentWidth"
                size="small"
                style="width: 80px"
                @change="handleSettingChange"
              >
                <a-select-option value="Fixed" v-if="localSettings.layout === 'top'">固定</a-select-option>
                <a-select-option value="Fluid">流式</a-select-option>
              </a-select>
            </template>
            内容区域宽度
          </a-list-item>
          <a-list-item>
            <template #extra>
              <a-switch
                v-model:checked="localSettings.fixedHeader"
                size="small"
                @change="handleSettingChange"
              />
            </template>
            固定 Header
          </a-list-item>
          <a-list-item>
            <template #extra>
              <a-switch
                v-model:checked="localSettings.fixedSidebar"
                size="small"
                :disabled="localSettings.layout === 'top'"
                @change="handleSettingChange"
              />
            </template>
            <span :style="{ opacity: localSettings.layout === 'top' ? 0.5 : 1 }">固定侧边菜单</span>
          </a-list-item>
          <a-list-item>
            <template #extra>
              <a-switch
                v-model:checked="localSettings.hideFooter"
                size="small"
                @change="handleSettingChange"
              />
            </template>
            隐藏 Footer
          </a-list-item>
          <a-list-item>
            <template #extra>
              <a-switch
                v-model:checked="localSettings.multiTab"
                size="small"
                @change="handleSettingChange"
              />
            </template>
            多页签模式
          </a-list-item>
        </a-list>
      </div>

      <a-divider />

      <!-- 系统配置 -->
      <div class="setting-drawer-block">
        <h3>系统配置</h3>
        <a-list :split="false">
          <a-list-item>
            <template #extra>
              <a-input
                v-model:value="systemConfig.title"
                size="small"
                style="width: 120px"
                @change="handleSystemConfigChange"
                placeholder="系统标题"
              />
            </template>
            系统标题
          </a-list-item>
          <a-list-item>
            <template #extra>
              <a-input
                v-model:value="systemConfig.copyright"
                size="small"
                style="width: 120px"
                @change="handleSystemConfigChange"
                placeholder="版权信息"
              />
            </template>
            版权信息
          </a-list-item>
          <a-list-item>
            <template #extra>
              <a-input
                v-model:value="systemConfig.version"
                size="small"
                style="width: 80px"
                @change="handleSystemConfigChange"
                placeholder="版本号"
              />
            </template>
            系统版本
          </a-list-item>
        </a-list>
      </div>

      <a-divider />

      <!-- 表格样式设置 -->
      <div class="setting-drawer-block">
        <h3>表格通用样式</h3>
        <a-list :split="false">
          <a-list-item>
            <template #extra>
              <a-select
                v-model:value="tableConfig.size"
                size="small"
                style="width: 80px"
                @change="handleTableConfigChange"
              >
                <a-select-option value="default">默认</a-select-option>
                <a-select-option value="middle">中等</a-select-option>
                <a-select-option value="small">紧凑</a-select-option>
              </a-select>
            </template>
            表格大小
          </a-list-item>
          <a-list-item>
            <template #extra>
              <a-switch
                v-model:checked="tableConfig.bordered"
                size="small"
                @change="handleTableConfigChange"
              />
            </template>
            显示边框
          </a-list-item>
          <a-list-item>
            <template #extra>
              <a-switch
                v-model:checked="tableConfig.fixHeader"
                size="small"
                @change="handleTableConfigChange"
              />
            </template>
            固定表头
          </a-list-item>
          <a-list-item>
            <template #extra>
              <a-switch
                v-model:checked="tableConfig.resizable"
                size="small"
                @change="handleTableConfigChange"
              />
            </template>
            允许调整列宽
          </a-list-item>
          <a-list-item>
            <template #extra>
              <a-select
                v-model:value="tableConfig.pageSize"
                size="small"
                style="width: 80px"
                @change="handleTableConfigChange"
              >
                <a-select-option v-for="size in tableConfig.pageSizeOptions" :key="size" :value="size">{{ size }}条/页</a-select-option>
              </a-select>
            </template>
            分页大小
          </a-list-item>
          <a-list-item>
            <template #extra>
              <a-switch
                v-model:checked="tableConfig.showTotal"
                size="small"
                @change="handleTableConfigChange"
              />
            </template>
            显示数据总数
          </a-list-item>
          <a-list-item>
            <template #extra>
              <a-switch
                v-model:checked="tableConfig.showSizeChanger"
                size="small"
                @change="handleTableConfigChange"
              />
            </template>
            显示分页选择器
          </a-list-item>
          <a-list-item>
            <template #extra>
              <a-switch
                v-model:checked="tableConfig.showQuickJumper"
                size="small"
                @change="handleTableConfigChange"
              />
            </template>
            显示快速跳转
          </a-list-item>
          <a-list-item>
            <template #extra>
              <a-switch
                v-model:checked="localSettings.tableStriped"
                size="small"
                @change="handleSettingChange"
              />
            </template>
            斑马条纹
          </a-list-item>
          <a-list-item>
            <template #extra>
              <a-switch
                v-model:checked="localSettings.tableShowHeader"
                size="small"
                @change="handleSettingChange"
              />
            </template>
            显示表头
          </a-list-item>
        </a-list>

        <!-- 添加保存按钮 -->
        <div class="setting-drawer-block-buttons">
          <a-button type="primary" @click="saveTableConfig">
            保存表格配置
          </a-button>
          <a-button @click="resetTableConfig">
            重置
          </a-button>
        </div>
      </div>

      <a-divider />

      <!-- 配置管理 -->
      <div class="setting-drawer-block">
        <h3>配置管理</h3>
        <div class="setting-drawer-block-buttons">
          <a-button type="primary" @click="saveAllSettings" style="width: 100%">
            <save-outlined />
            保存所有配置
          </a-button>
          <a-button @click="exportSettings" style="width: 100%">
            <download-outlined />
            导出配置
          </a-button>
          <a-upload
            :show-upload-list="false"
            :before-upload="importSettings"
            accept=".json"
            style="width: 100%"
          >
            <a-button style="width: 100%">
              <upload-outlined />
              导入配置
            </a-button>
          </a-upload>
          <a-button danger @click="resetAllSettings" style="width: 100%">
            <redo-outlined />
            重置所有配置
          </a-button>
        </div>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits, onMounted, computed } from 'vue';
import {
  CheckOutlined,
  SaveOutlined,
  DownloadOutlined,
  UploadOutlined,
  RedoOutlined
} from '@ant-design/icons-vue';
import { themeConfig } from '@/layout/theme.js';
import { useTableConfigStore } from '@/stores/tableConfig';
import { useAppStore } from '@/stores/app';
import { message, Modal } from 'ant-design-vue';

const tableConfigStore = useTableConfigStore();
const appStore = useAppStore();

// 从 localStorage 获取已保存的配置，如果没有则使用默认值
const savedConfig = JSON.parse(localStorage.getItem('table-config') || '{}');

// 系统配置
const systemConfig = ref({
  title: appStore.systemConfig.title,
  copyright: appStore.systemConfig.copyright,
  version: appStore.systemConfig.version,
  description: appStore.systemConfig.description,
  keywords: appStore.systemConfig.keywords
});

const tableConfig = ref({
  size: savedConfig.size || tableConfigStore.size || 'middle',
  bordered: savedConfig.bordered ?? tableConfigStore.bordered ?? true,
  showHeader: savedConfig.showHeader ?? tableConfigStore.showHeader ?? true,
  headerBold: savedConfig.headerBold ?? tableConfigStore.headerBold ?? true,
  stripe: savedConfig.stripe ?? tableConfigStore.stripe ?? false,
  headerBgColor: savedConfig.headerBgColor || tableConfigStore.headerBgColor || '#f5f7fa',
  loading: savedConfig.loading ?? tableConfigStore.loading ?? false,
  fixHeader: savedConfig.fixHeader ?? tableConfigStore.fixHeader ?? false,
  resizable: savedConfig.resizable ?? tableConfigStore.resizable ?? false,
  pageSize: savedConfig.pageSize || tableConfigStore.pageSize || 10,
  pageSizeOptions: savedConfig.pageSizeOptions || tableConfigStore.pageSizeOptions || ['10', '20', '50', '100'],
  showTotal: savedConfig.showTotal ?? tableConfigStore.showTotal ?? true,
  showSizeChanger: savedConfig.showSizeChanger ?? tableConfigStore.showSizeChanger ?? true,
  showQuickJumper: savedConfig.showQuickJumper ?? tableConfigStore.showQuickJumper ?? true
});

// 组件初始化时同步配置到store
onMounted(() => {
  // 确保初始配置同步到store
  tableConfigStore.updateConfig(tableConfig.value);
  tableConfigStore.updatePagination({
    pageSize: tableConfig.value.pageSize,
    showSizeChanger: tableConfig.value.showSizeChanger,
    showQuickJumper: tableConfig.value.showQuickJumper,
    showTotal: tableConfig.value.showTotal
  });
});

// 使用watch来同步更新本地存储和store
watch(tableConfig, (newConfig) => {
  localStorage.setItem('table-config', JSON.stringify(newConfig));

  // 更新store中的配置
  tableConfigStore.updateConfig(newConfig);

  // 同时更新分页配置
  tableConfigStore.updatePagination({
    pageSize: newConfig.pageSize,
    showSizeChanger: newConfig.showSizeChanger,
    showQuickJumper: newConfig.showQuickJumper,
    showTotal: newConfig.showTotal
  });
}, { deep: true });

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  settings: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update:visible', 'update:settings']);

const localSettings = ref({ ...props.settings });

// 预设主题色列表
const colorList = [
  { color: '#1677ff', key: '默认蓝' },
  { color: '#f5222d', key: '薄暮红' },
  { color: '#fa541c', key: '火山橙' },
  { color: '#faad14', key: '日暮黄' },
  { color: '#13c2c2', key: '明青色' },
  { color: '#52c41a', key: '极光绿' },
  { color: '#722ed1', key: '酱紫色' },
  { color: '#eb2f96', key: '粉红色' }
];

// 监听外部设置变化
watch(() => props.settings, (newSettings) => {
  localSettings.value = { ...newSettings };
}, { deep: true });

// 监听系统配置变化
watch(() => appStore.systemConfig, (newConfig) => {
  systemConfig.value = { ...newConfig };
}, { deep: true });

// 处理主题切换
const handleThemeChange = (theme) => {
  localSettings.value = {
    ...localSettings.value,
    darkMode: theme === 'dark',
    primaryColor: localSettings.value.primaryColor // 保持当前主题色不变
  };
  handleSettingChange();
};

// 处理主题色切换
const handleColorChange = (color) => {
  localSettings.value = {
    ...localSettings.value,
    primaryColor: color
  };
  handleSettingChange();
};

// 处理布局切换
const handleLayoutChange = (layout) => {
  localSettings.value.layout = layout;
  // 切换为顶部菜单时，自动取消固定侧边栏
  if (layout === 'top') {
    localSettings.value.fixedSidebar = false;
  }
  handleSettingChange();
};

// 处理设置变更
const handleSettingChange = () => {
  emit('update:settings', { ...localSettings.value });
};

// 处理表格配置变更
const handleTableConfigChange = () => {
  // 仅在内存中更新配置
  tableConfig.value = { ...tableConfig.value };
};

// 保存表格配置
const saveTableConfig = () => {
  // 更新 store
  tableConfigStore.updateConfig(tableConfig.value);
  // 保存到 localStorage
  localStorage.setItem('table-config', JSON.stringify(tableConfig.value));
  message.success('表格配置已保存');
};

// 重置表格配置
const resetTableConfig = () => {
  // 重置store配置
  tableConfigStore.resetConfig();

  // 重置本地配置
  const defaultConfig = {
    size: 'middle',
    bordered: true,
    showHeader: true,
    headerBold: true,
    stripe: false,
    headerBgColor: '#f5f7fa',
    loading: false,
    fixHeader: false,
    resizable: false,
    pageSize: 10,
    pageSizeOptions: ['10', '20', '50', '100'],
    showTotal: true,
    showSizeChanger: true,
    showQuickJumper: true
  };

  tableConfig.value = defaultConfig;

  // 同步更新分页配置到store
  tableConfigStore.updatePagination({
    pageSize: defaultConfig.pageSize,
    showSizeChanger: defaultConfig.showSizeChanger,
    showQuickJumper: defaultConfig.showQuickJumper,
    showTotal: defaultConfig.showTotal
  });

  localStorage.setItem('table-config', JSON.stringify(defaultConfig));
  message.success('表格配置已重置');
};

// 处理系统配置变更
const handleSystemConfigChange = () => {
  appStore.updateSystemConfig(systemConfig.value);
  message.success('系统配置已更新');
};

// 保存所有设置
const saveAllSettings = () => {
  // 保存布局设置
  emit('update:settings', { ...localSettings.value });

  // 保存系统配置
  appStore.updateSystemConfig(systemConfig.value);

  // 保存表格配置
  tableConfigStore.updateConfig(tableConfig.value);

  message.success('所有配置已保存');
};

// 导出设置
const exportSettings = () => {
  appStore.exportSettings();
  message.success('配置已导出');
};

// 导入设置
const importSettings = (file) => {
  const reader = new FileReader();
  reader.onload = (e) => {
    try {
      const config = JSON.parse(e.target.result);

      Modal.confirm({
        title: '确认导入配置',
        content: '导入配置将覆盖当前所有设置，是否继续？',
        onOk() {
          // 导入布局设置
          if (config.layoutSettings) {
            localSettings.value = { ...config.layoutSettings };
            emit('update:settings', { ...config.layoutSettings });
          }

          // 导入系统配置
          if (config.systemConfig) {
            systemConfig.value = { ...config.systemConfig };
            appStore.updateSystemConfig(config.systemConfig);
          }

          // 导入表格配置
          if (config.tableConfig) {
            tableConfig.value = { ...config.tableConfig };
            tableConfigStore.updateConfig(config.tableConfig);
          }

          message.success('配置导入成功');
        }
      });
    } catch (error) {
      message.error('配置文件格式错误');
    }
  };
  reader.readAsText(file);
  return false; // 阻止默认上传行为
};

// 重置所有设置
const resetAllSettings = () => {
  Modal.confirm({
    title: '确认重置',
    content: '重置将清除所有自定义配置，恢复到默认状态，是否继续？',
    onOk() {
      // 重置布局设置
      appStore.resetSettings();
      localSettings.value = { ...appStore.layoutSettings };

      // 重置系统配置
      const defaultSystemConfig = {
        title: 'BearJia Admin',
        copyright: 'Copyright © 2024 BearJia',
        version: '1.0.0',
        description: 'BearJia后台管理系统',
        keywords: 'BearJia,Admin,Vue3,Ant Design Vue'
      };
      systemConfig.value = defaultSystemConfig;
      appStore.updateSystemConfig(defaultSystemConfig);

      // 重置表格配置
      resetTableConfig();

      message.success('所有配置已重置');
    }
  });
};

</script>

<style lang="less" scoped>
.setting-drawer {
  &-content {
    padding: 12px;
  }

  &-block {
    margin-bottom: 24px;

    h3 {
      margin-bottom: 12px;
      font-size: 14px;
      color: var(--text-color, rgba(0, 0, 0, 0.85));
    }

    &-checbox {
      display: flex;
      gap: 16px;

      &-item {
        position: relative;
        cursor: pointer;
        border-radius: 4px;
        overflow: hidden;
        border: 2px solid transparent;
        transition: all 0.3s;

        &:hover {
          transform: scale(1.05);
        }

        &.active {
          border-color: var(--primary-color);
        }

        img {
          width: 48px;
          height: 48px;
          display: block;
        }

        .setting-drawer-theme-color-icon {
          position: absolute;
          top: 0;
          right: 0;
          width: 16px;
          height: 16px;
          color: var(--primary-color, #1890ff);
          font-size: 14px;
          font-weight: bold;
        }
      }
    }
  }

  &-theme-color {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    &-block {
      width: 20px;
      height: 20px;
      border-radius: 2px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
    }
  }
}

:deep(.ant-list-item) {
  padding: 12px 0;
  justify-content: space-between;
  color: var(--text-color, rgba(0, 0, 0, 0.85));
}

:deep(.ant-radio-group) {
  display: flex;
}

.setting-drawer-block-buttons {
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  justify-content: center;

  &:not(:last-child) {
    flex-direction: row;
  }
}

// 暗色主题下的样式
:global(.dark-theme) {
  .setting-drawer-block {
    h3 {
      color: var(--text-color);
    }
  }

  :deep(.ant-list-item) {
    color: var(--text-color);
  }

  :deep(.ant-select-selection-item) {
    color: var(--text-color);
  }

  :deep(.ant-switch) {
    background-color: rgba(255, 255, 255, 0.2);

    &-checked {
      background-color: var(--primary-color);
    }
  }
}
</style>
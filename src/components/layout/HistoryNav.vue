<template>
  <div class="history-nav">
    <div class="history-tags">
      <a-tag
        v-for="(item, index) in historyList"
        :key="index"
        :closable="item.path !== '/home/<USER>'"
        @close.prevent="removeHistory(index)"
        :class="{ active: currentPath === item.path }"
        @click="handleClick(item)"
      >
        {{ item.title }}
        <a-dropdown v-if="index === activeIndex" :trigger="['hover']">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="refreshPage">
                <reload-outlined /> 刷新页面
              </a-menu-item>
              <a-menu-item key="2" @click="closeCurrentPage">
                <close-outlined /> 关闭页面
              </a-menu-item>
              <a-menu-item key="3" @click="closeOtherPages">
                <minus-outlined /> 关闭其他
              </a-menu-item>
              <a-menu-item key="4" @click="closeAllPages">
                <close-circle-outlined /> 关闭所有
              </a-menu-item>
            </a-menu>
          </template>
          <down-outlined class="dropdown-icon" />
        </a-dropdown>
      </a-tag>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { useAppStore } from '@/stores/app';
import { usePermissionStore } from '@/stores/permission';
import { ReloadOutlined, CloseOutlined, MinusOutlined, CloseCircleOutlined, DownOutlined } from '@ant-design/icons-vue';

const router = useRouter();
const route = useRoute();
const appStore = useAppStore();
const permissionStore = usePermissionStore();

// 历史记录列表，使用ref来保持响应性
const historyList = ref([]);
const MAX_HISTORY = 10; // 最大历史记录数
const activeIndex = ref(-1); // 当前激活的标签索引

// 获取当前路径
const currentPath = ref(route.path);

// 从store获取菜单名称
const getMenuTitle = async (path) => {
  let menuList = permissionStore.sidebarRouters || [];
  
  // 如果menuList为空，等待一段时间后重试
  let retryCount = 0;
  while (menuList.length === 0 && retryCount < 3) {
    await new Promise(resolve => setTimeout(resolve, 500));
    menuList = permissionStore.sidebarRouters || [];
    retryCount++;
  }

  // 工作台特殊处理
  if (path === '/home/<USER>') {
    return '工作台';
  }

  const findMenuTitle = (menus) => {
    for (const menu of menus) {
      if (menu.path === path) return menu.meta?.title || menu.name;
      if (menu.children) {
        const title = findMenuTitle(menu.children);
        if (title) return title;
      }
    }
    return null;
  };
  return findMenuTitle(menuList) || route.meta?.title || '未知页面';
};

// 添加历史记录
const addHistory = async (item) => {
  try {
    // 检查是否已存在相同路径
    const index = historyList.value.findIndex(h => h.path === item.path);
    
    // 如果存在，先删除旧的
    if (index > -1) {
      historyList.value.splice(index, 1);
    }
    
    // 如果是工作台，确保它始终在第一位
    if (item.path === '/home/<USER>') {
      historyList.value.unshift(item);
    } else {
      // 非工作台页面，插入到工作台后面
      const workbenchIndex = historyList.value.findIndex(h => h.path === '/home/<USER>');
      if (workbenchIndex === -1) {
        // 如果没有工作台，先添加工作台
        historyList.value.unshift({
          path: '/home/<USER>',
          title: '工作台'
        });
        historyList.value.push(item);
      } else {
        historyList.value.splice(workbenchIndex + 1, 0, item);
      }
    }
    
    // 限制历史记录数量
    if (historyList.value.length > MAX_HISTORY) {
      // 保留工作台和最近的记录
      const workbenchItem = historyList.value.find(h => h.path === '/home/<USER>');
      historyList.value = [
        workbenchItem,
        ...historyList.value.filter(h => h.path !== '/home/<USER>').slice(0, MAX_HISTORY - 1)
      ];
    }
    
    // 保存到localStorage
    localStorage.setItem('historyList', JSON.stringify(historyList.value));
  } catch (error) {
    console.error('添加历史记录失败:', error);
  }
};

// 监听路由变化
watch(
  () => route.path,
  async (newPath) => {
    if (!newPath) return;
    
    // 获取菜单标题
    const title = await getMenuTitle(newPath);
    
    // 添加新的历史记录
    await addHistory({
      path: newPath,
      title: title
    });
    
    // 更新当前路径和激活索引
    currentPath.value = newPath;
    activeIndex.value = historyList.value.findIndex(item => item.path === newPath);
  },
  { immediate: true }
);

// 处理点击事件
const handleClick = async (item) => {
  try {
    if (item.path === currentPath.value) return;
    await router.push(item.path);
  } catch (error) {
    console.error('页面跳转失败:', error);
    message.error('页面跳转失败');
  }
};

// 移除历史记录
const removeHistory = async (index) => {
  try {
    const removedItem = historyList.value[index];
    
    // 不允许删除工作台标签
    if (removedItem.path === '/home/<USER>') {
      return;
    }
    
    historyList.value.splice(index, 1);
    
    // 更新localStorage
    localStorage.setItem('historyList', JSON.stringify(historyList.value));
    
    // 如果删除的是当前页面，则跳转到最近的历史记录
    if (currentPath.value === removedItem.path) {
      const targetPath = index === 0 ? historyList.value[0].path : historyList.value[index - 1].path;
      await router.push(targetPath);
    }
  } catch (error) {
    console.error('删除历史记录失败:', error);
    message.error('删除历史记录失败');
  }
};

// 刷新当前页面
const refreshPage = () => {
  router.go(0);
};

// 关闭当前页面
const closeCurrentPage = async () => {
  if (activeIndex.value > -1 && historyList.value[activeIndex.value].path !== '/home/<USER>') {
    await removeHistory(activeIndex.value);
  }
};

// 关闭其他页面
const closeOtherPages = () => {
  const currentItem = historyList.value[activeIndex.value];
  const workbenchItem = historyList.value.find(item => item.path === '/home/<USER>');
  
  historyList.value = [workbenchItem];
  if (currentItem.path !== '/home/<USER>') {
    historyList.value.push(currentItem);
  }
  
  localStorage.setItem('historyList', JSON.stringify(historyList.value));
};

// 关闭所有页面
const closeAllPages = async () => {
  historyList.value = [{
    path: '/home/<USER>',
    title: '工作台'
  }];
  localStorage.setItem('historyList', JSON.stringify(historyList.value));
  if (currentPath.value !== '/home/<USER>') {
    await router.push('/home/<USER>');
  }
};

// 组件挂载时从localStorage恢复历史记录
onMounted(() => {
  try {
    // 清空并只保留工作台
    historyList.value = [{
      path: '/home/<USER>',
      title: '工作台'
    }];
    
    // 清空本地存储的历史记录
    localStorage.removeItem('historyList');
    
    // 保存新的历史记录
    localStorage.setItem('historyList', JSON.stringify(historyList.value));
  } catch (error) {
    console.error('初始化历史记录失败:', error);
  }
});
</script>

<style scoped>
.history-nav {
  height: 40px;
  padding: 0 16px;
  /* background: #fafbfc; */
  /* border-bottom: 1px solid #f0f0f0; */
  display: flex;
  align-items: center;
  /* box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08); */
}

.history-tags {
  display: flex;
  flex-wrap: nowrap;
  gap: 4px;
  align-items: center;
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.history-tags::-webkit-scrollbar {
  display: none;
}

.history-tags :deep(.ant-tag) {
  margin: 0;
  padding: 4px 8px;
  cursor: pointer;
  user-select: none;
  transition: all 0.3s;
  border-radius: 6px;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
}

.history-tags :deep(.ant-tag.active) {
  color: var(--ant-primary-color);
  background: var(--ant-primary-1);
  border-color: var(--ant-primary-3);
}

.history-tags :deep(.ant-tag:hover) {
  color: var(--ant-primary-color);
  border-color: var(--ant-primary-color);
}

.history-tags :deep(.anticon-close) {
  margin-left: 4px;
  font-size: 12px;
  vertical-align: middle;
}

.dropdown-icon {
  margin-left: 4px;
  font-size: 12px;
  opacity: 0.6;
}

:deep(.ant-dropdown-menu-item) {
  min-width: 120px;
}
</style>
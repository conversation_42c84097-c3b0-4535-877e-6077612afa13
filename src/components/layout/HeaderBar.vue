<template>
  <a-layout-header class="header-bar">
    <a-row>
      <a-col :span="12" class="header-left">
        <menu-unfold-outlined
          v-if="collapsed"
          @click="toggleCollapse"
          class="trigger"
        />
        <menu-fold-outlined
          v-else
          @click="toggleCollapse"
          class="trigger"
        />
        <span class="header-title">
          {{ currentFatherMenuTitle }} / {{ currentMenuTitle }}
        </span>
      </a-col>
      <a-col :span="12" class="header-right">
        <!-- 加载动画 -->
        <a-spin :spinning="loading" style="margin-right: 16px;" />
        
        <!-- 刷新按钮 -->
        <a-tooltip placement="bottom" title="刷新页面">
          <a-button type="link" class="header-icon-btn" @click="refreshCurrentPage">
            <ReloadOutlined />
          </a-button>
        </a-tooltip>
        
        <!-- 布局设置 -->
        <a-tooltip placement="bottom" title="布局设置">
          <a-button type="link" class="header-icon-btn" @click="$emit('showSettings')">
            <SettingOutlined />
          </a-button>
        </a-tooltip>

        <!-- 用户头像和下拉菜单 -->
        <a-dropdown>
          <a class="ant-dropdown-link" @click.prevent>
            <a-avatar :size="32" :src="userAvatar">
              <template #icon><UserOutlined /></template>
            </a-avatar>
            <span class="username">{{ userName }}</span>
          </a>
          <template #overlay>
            <a-menu>
              <a-menu-item key="personalCenter" @click="$emit('personalCenter')">
                <UserOutlined class="menu-icon" />
                <span>个人中心</span>
              </a-menu-item>
              <a-menu-item key="settings" @click="$emit('showSettings')">
                <SettingOutlined class="menu-icon" />
                <span>布局设置</span>
              </a-menu-item>
              <a-menu-item key="logout" @click="$emit('logout')">
                <LogoutOutlined class="menu-icon" />
                <span>退出登录</span>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </a-col>
    </a-row>
  </a-layout-header>
</template>

<script setup>
import { computed, defineEmits, defineProps } from 'vue';
import { useUserStore } from '@/stores/user';

import {
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  SettingOutlined,
  UserOutlined,
  LogoutOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue';

const userStore = useUserStore();

const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false
  },
  currentFatherMenuTitle: {
    type: String,
    default: ''
  },
  currentMenuTitle: {
    type: String,
    default: ''
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits([
  'update:collapsed',
  'showSettings',
  'personalCenter',
  'logout',
  'refreshPage'
]);

const userAvatar = computed(() => userStore.avatar || '../../assets/images/profile.jpg');
const userName = computed(() => userStore.nickName || '用户');

const toggleCollapse = () => {
  emit('update:collapsed', !props.collapsed);
};

const refreshCurrentPage = () => {
  emit('refreshPage');
};
</script>

<style lang="less" scoped>
.menu-icon {
  margin-right: 8px;
}
.header-bar {
  background: #fafbfc;
  padding: 0;
  height: 56px;
  line-height: 56px;

  .trigger {
    color: rgba(0, 0, 0, 0.65);
    padding: 0 24px;
    cursor: pointer;
    transition: color 0.3s, transform 0.3s;

    &:hover {
      color: #1890ff;
      transform: rotate(180deg);
    }
  }

  .header-left {
    .header-title {
      color: rgba(0, 0, 0, 0.65);
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 24px;
    
    .header-icon-btn {
      color: rgba(0, 0, 0, 0.65);
      padding: 0 12px;
      transition: background 0.3s, color 0.3s, transform 0.3s;
      
      &:hover {
        background: rgba(0, 0, 0, 0.04);
        color: var(--primary-color);
        transform: rotate(360deg);
      }
    }
    
    .username {
      margin-left: 8px;
      color: rgba(0, 0, 0, 0.65);
    }

    .ant-dropdown-link {
      transition: all 0.3s ease;
      padding: 4px 8px;
      border-radius: 4px;
      
      &:hover {
        background: rgba(0, 0, 0, 0.02);
        
        .ant-avatar {
          transform: rotate(360deg);
        }
      }
    }
    
    .ant-avatar {
      transition: transform 0.5s;
    }
  }
}
</style>
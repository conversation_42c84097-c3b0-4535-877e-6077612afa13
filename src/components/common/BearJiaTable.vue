<template>
  <div class="bearjia-table-wrapper">
    <a-table
      v-bind="tableProps"
      :columns="computedColumns"
      :data-source="dataSource"
      :pagination="paginationProps"
      :loading="loading"
      @change="handleTableChange"
      :class="tableClasses"
    >
      <!-- 传递所有插槽 -->
      <template v-for="(_, name) in $slots" #[name]="slotData">
        <slot :name="name" v-bind="slotData" />
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { computed, watch } from 'vue';
import { useTableConfigStore } from '@/stores/tableConfig';

const props = defineProps({
  columns: {
    type: Array,
    required: true
  },
  dataSource: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  pagination: {
    type: [Object, Boolean],
    default: true
  },
  // 允许覆盖全局配置
  size: String,
  bordered: Boolean,
  showHeader: <PERSON><PERSON><PERSON>,
  scroll: Object,
  rowSelection: [Object, Boolean]
});

const emit = defineEmits(['change', 'select', 'selectAll']);

const tableConfigStore = useTableConfigStore();

// 初始化时加载配置
tableConfigStore.loadConfig();

// 计算表格属性
const tableProps = computed(() => {
  const globalProps = tableConfigStore.getTableProps();
  
  return {
    size: props.size ?? globalProps.size,
    bordered: props.bordered ?? globalProps.bordered,
    showHeader: props.showHeader ?? globalProps.showHeader,
    scroll: props.scroll ?? globalProps.scroll,
    rowSelection: props.rowSelection !== undefined ? props.rowSelection : globalProps.rowSelection
  };
});

// 计算分页属性
const paginationProps = computed(() => {
  if (props.pagination === false) {
    return false;
  }
  
  const globalPagination = tableConfigStore.getPaginationProps();
  
  if (typeof props.pagination === 'object') {
    return {
      ...globalPagination,
      ...props.pagination
    };
  }
  
  return globalPagination;
});

// 计算列配置
const computedColumns = computed(() => {
  return props.columns.map(column => {
    // 如果启用了表头加粗
    if (tableConfigStore.headerBold && !column.customHeaderCell) {
      column.customHeaderCell = () => ({
        style: { fontWeight: 'bold' }
      });
    }
    
    return column;
  });
});

// 计算表格样式类
const tableClasses = computed(() => {
  const classes = [];
  
  if (tableConfigStore.stripe) {
    classes.push('bearjia-table-striped');
  }
  
  return classes;
});

// 处理表格变化
const handleTableChange = (pagination, filters, sorter, extra) => {
  // 更新store中的分页信息
  if (pagination) {
    tableConfigStore.updatePagination({
      current: pagination.current,
      pageSize: pagination.pageSize,
      total: pagination.total
    });
  }
  
  emit('change', pagination, filters, sorter, extra);
};

// 监听全局配置变化
watch(() => tableConfigStore.$state, () => {
  // 配置变化时可以执行一些操作
}, { deep: true });
</script>

<style lang="less" scoped>
.bearjia-table-wrapper {
  :deep(.ant-table) {
    // 应用全局表格配置
    .ant-table-thead > tr > th {
      background-color: v-bind('tableConfigStore.headerBgColor');
      font-weight: v-bind('tableConfigStore.headerBold ? "bold" : "normal"');
    }
    
    // 斑马条纹样式
    &.bearjia-table-striped {
      .ant-table-tbody > tr:nth-child(even) > td {
        background-color: rgba(0, 0, 0, 0.02);
      }
      
      .ant-table-tbody > tr:nth-child(even):hover > td {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }
    
    // 暗色主题下的斑马条纹
    :global(.dark-theme) &.bearjia-table-striped {
      .ant-table-tbody > tr:nth-child(even) > td {
        background-color: rgba(255, 255, 255, 0.02);
      }
      
      .ant-table-tbody > tr:nth-child(even):hover > td {
        background-color: rgba(255, 255, 255, 0.04);
      }
    }
  }
}
</style>

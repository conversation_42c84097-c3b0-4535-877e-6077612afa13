<template>
  <div class="table-setting">
    <a-tooltip title="刷新">
      <a-button 
        type="text" 
        :loading="refreshLoading" 
        @click="handleRefresh"
        :icon="h(ReloadOutlined)"
      />
    </a-tooltip>
    
    <a-tooltip title="密度">
      <a-dropdown placement="bottomRight">
        <a-button type="text" :icon="h(ColumnHeightOutlined)" />
        <template #overlay>
          <a-menu 
            :selected-keys="[tableSize]" 
            @click="handleTableSizeChange"
          >
            <a-menu-item key="large">默认</a-menu-item>
            <a-menu-item key="middle">中等</a-menu-item>
            <a-menu-item key="small">紧凑</a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </a-tooltip>
    
    <a-tooltip title="列设置">
      <a-popover 
        placement="bottomRight" 
        trigger="click"
        :overlay-style="{ padding: 0 }"
      >
        <template #content>
          <div class="table-column-setting">
            <div class="setting-header">
              <a-checkbox 
                :indeterminate="indeterminate"
                :checked="checkAll"
                @change="onCheckAllChange"
              >
                列展示
              </a-checkbox>
              <a-button type="link" size="small" @click="resetColumns">
                重置
              </a-button>
            </div>
            <a-divider style="margin: 8px 0" />
            <div class="setting-list">
              <a-checkbox-group v-model:value="checkedList" @change="onCheckChange">
                <div 
                  v-for="column in settingColumns" 
                  :key="column.dataIndex"
                  class="setting-item"
                >
                  <a-checkbox :value="column.dataIndex">
                    {{ column.title }}
                  </a-checkbox>
                </div>
              </a-checkbox-group>
            </div>
          </div>
        </template>
        <a-button type="text" :icon="h(SettingOutlined)" />
      </a-popover>
    </a-tooltip>
  </div>
</template>

<script setup>
import { ref, computed, watch, h } from 'vue';
import { 
  ReloadOutlined, 
  ColumnHeightOutlined, 
  SettingOutlined 
} from '@ant-design/icons-vue';

const props = defineProps({
  tableSize: {
    type: String,
    default: 'middle'
  },
  refreshLoading: {
    type: Boolean,
    default: false
  },
  modelValue: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['refresh', 'update:tableSize', 'update:modelValue']);

// 表格尺寸
const tableSize = ref(props.tableSize);

// 列设置相关
const settingColumns = ref([]);
const checkedList = ref([]);
const originalColumns = ref([]);

// 初始化列设置
const initColumns = () => {
  originalColumns.value = [...props.modelValue];
  settingColumns.value = props.modelValue
    .filter(col => col.dataIndex && col.title)
    .map(col => ({
      dataIndex: col.dataIndex,
      title: col.title,
      visible: true
    }));
  checkedList.value = settingColumns.value.map(col => col.dataIndex);
};

// 监听 modelValue 变化
watch(() => props.modelValue, () => {
  if (props.modelValue.length > 0) {
    initColumns();
  }
}, { immediate: true });

// 全选状态
const checkAll = computed(() => {
  return checkedList.value.length === settingColumns.value.length;
});

// 半选状态
const indeterminate = computed(() => {
  return checkedList.value.length > 0 && checkedList.value.length < settingColumns.value.length;
});

// 刷新
const handleRefresh = () => {
  emit('refresh');
};

// 表格尺寸变化
const handleTableSizeChange = ({ key }) => {
  tableSize.value = key;
  emit('update:tableSize', key);
};

// 全选/取消全选
const onCheckAllChange = (e) => {
  if (e.target.checked) {
    checkedList.value = settingColumns.value.map(col => col.dataIndex);
  } else {
    checkedList.value = [];
  }
  updateColumns();
};

// 列选择变化
const onCheckChange = () => {
  updateColumns();
};

// 更新列显示
const updateColumns = () => {
  const newColumns = originalColumns.value.filter(col => {
    if (!col.dataIndex) return true; // 保留操作列等没有 dataIndex 的列
    return checkedList.value.includes(col.dataIndex);
  });
  emit('update:modelValue', newColumns);
};

// 重置列
const resetColumns = () => {
  checkedList.value = settingColumns.value.map(col => col.dataIndex);
  emit('update:modelValue', [...originalColumns.value]);
};
</script>

<style scoped>
.table-setting {
  display: flex;
  align-items: center;
  gap: 4px;
}

.table-column-setting {
  width: 200px;
  max-height: 300px;
  overflow-y: auto;
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
}

.setting-list {
  padding: 0 12px 8px;
}

.setting-item {
  display: block;
  margin-bottom: 8px;
}

.setting-item:last-child {
  margin-bottom: 0;
}
</style>

// 主题配置
export const themeConfig = {
  light: {
    primaryColor: '#1677ff',
    textColor: 'rgba(0, 0, 0, 0.85)',
    menuBg: '#ffffff',
    menuItemColor: 'rgba(0, 0, 0, 0.85)',
    menuHighlightColor: '#ffffff',
    borderColor: '#f0f0f0'
  },
  dark: {
    primaryColor: '#1677ff',
    textColor: 'rgba(255, 255, 255, 0.85)',
    menuBg: '#141414',
    menuItemColor: 'rgba(255, 255, 255, 0.65)',
    menuHighlightColor: '#ffffff',
    borderColor: '#303030'
  }
};

// CSS变量名映射
export const cssVars = {
  primaryColor: '--primary-color',
  headerBg: '--header-bg',
  textColor: '--text-color',
  menuBg: '--menu-bg',
  menuItemColor: '--menu-item-color',
  menuHighlightBg: '--menu-highlight-bg',
  menuHighlightColor: '--menu-highlight-color',
  borderColor: '--border-color'
}; 
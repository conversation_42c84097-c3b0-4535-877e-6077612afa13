<template>
  <div class="app-container" :style="cssVars">
    <!-- 主页布局 -->
    <a-layout class="layout-container">
      <SideMenu
        v-model:collapsed="collapsed"
        :menu-data="sidebarRouters"
        :layout-settings="layoutSettings"
        @menu-select="handleMenuSelect"
      />
      <a-layout style="background: #fafbfc;">
        <HeaderBar
          v-model:collapsed="collapsed"
          :current-father-menu-title="headerInfo.currentFatherMenuTitle"
          :current-menu-title="headerInfo.currentMenuTitle"
          :loading="loading"
          @show-settings="showDrawer"
          @personal-center="enterPersonalCenter"
          @logout="handleLogout"
          @refresh-page="refreshCurrentPage"
        />
        <HistoryNav class="layout-container__history" />
        <a-layout-content class="layout-container__content">
                  <div class="content-wrapper" style="overflow-y: auto; height: calc(100vh - 64px);">
                    <a-config-provider :locale="zhCN">
                      <router-view></router-view>
                    </a-config-provider>
                  </div>
                </a-layout-content>
      </a-layout>

      <!-- 设置抽屉组件 -->
      <SettingDrawer
        v-model:visible="drawerVisible"
        :settings="layoutSettings"
        @update:settings="handleSettingsChange"
      />
    </a-layout>
  </div>
</template>

<script setup>
import { ref, computed, reactive, watch, nextTick, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useUserStore } from '@/stores/user';
import { useAppStore } from '@/stores/app';
import { usePermissionStore } from '@/stores/permission';
import { useTagsViewStore } from '@/stores/tagsView';
import BearJiaUtil from '@/utils/BearJiaUtil.js';
// 国际化：显示中文
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { message } from 'ant-design-vue';

// 将侧边栏和头部组件拆分
import SideMenu from '@/components/layout/SideMenu.vue';
import HeaderBar from '@/components/layout/HeaderBar.vue';
import HistoryNav from '@/components/layout/HistoryNav.vue';
// 将布局设置抽屉组件拆分
import SettingDrawer from '@/components/layout/SettingDrawer.vue';

dayjs.locale('zh-cn');

const vueRouter = useRouter();
const vueRoute = useRoute();
const userRouters = vueRouter.getRoutes();

// 获取 store
const userStore = useUserStore();
const appStore = useAppStore();
const permissionStore = usePermissionStore();
const tagsViewStore = useTagsViewStore();

// 左侧边栏是否折叠：默认不折叠
const collapsed = ref(false);
const loading = ref(false);
const drawerVisible = ref(false);

// 初始化 headerInfo
const headerInfo = reactive({
  currentFatherMenuTitle: '主页',
  currentMenuTitle: '工作台'
});

// 菜单相关状态数据
const menuState = reactive({
  // 所有一级菜单节点
  rootSubmenuKeys: [],
  // 展开的父菜单节点
  openedFaterMenuKeys: [],
  // 当前选中的菜单项
  selectedMenuKeys: [],
});

// 通过从后端获取的用户可以访问的菜单信息生产前端菜单列表
const sidebarRouters = computed(() => permissionStore.sidebarRouters || []);

// 布局设置
const layoutSettings = computed(() => appStore.layoutSettings || {
  primaryColor: '#1677ff',
  darkMode: false,
  navMode: 'side',
  menuTheme: 'light',
  layout: 'mix',
  contentWidth: 'fluid',
  fixedHeader: true,
  fixedSidebar: true,
  splitMenus: false,
  colorWeak: false,
  multiTab: true
});

// 计算主题相关的 CSS 变量
const themeVars = computed(() => {
  const primaryColor = layoutSettings.value?.primaryColor || '#1677ff';
  const primaryColorHover = `color-mix(in srgb, ${primaryColor} 90%, white)`;
  const primaryColorActive = `color-mix(in srgb, ${primaryColor} 110%, black)`;
  const primary1 = `color-mix(in srgb, ${primaryColor} 20%, white)`;

  // 更新根变量
  const root = document.documentElement;

  // 更新 Ant Design 基础变量
  root.style.setProperty('--ant-primary-color', primaryColor);
  root.style.setProperty('--ant-primary-color-hover', primaryColorHover);
  root.style.setProperty('--ant-primary-color-active', primaryColorActive);
  root.style.setProperty('--ant-primary-1', primary1);

  // 更新主题色变量
  root.style.setProperty('--primary-color', primaryColor);
  root.style.setProperty('--primary-color-hover', primaryColorHover);
  root.style.setProperty('--primary-color-active', primaryColorActive);
  root.style.setProperty('--primary-1', primary1);

  // 更新按钮相关变量
  root.style.setProperty('--btn-primary-bg', primaryColor);
  root.style.setProperty('--btn-hover-bg', primaryColorHover);
  root.style.setProperty('--btn-active-bg', primaryColorActive);

  return {
    primaryColor,
    primaryColorHover,
    primaryColorActive,
    primary1
  };
});

// 计算 CSS 变量
const cssVars = computed(() => ({})); // 移除所有变量，现在通过 root 设置

// 设置一级父菜单
const initRootMenus = () => {
  if (sidebarRouters.value && Array.isArray(sidebarRouters.value)) {
    sidebarRouters.value.forEach((element) => {
      menuState.rootSubmenuKeys.push(element.path);
    });
  }
  // 将工作台菜单添加到一级菜单节点数组中
  menuState.rootSubmenuKeys.push('workbench');
};

// 初始化路由状态
const initRouteState = () => {
  if (vueRoute.path) {
    let arr = vueRoute.path.split('/');
    // 如果有父菜单，获取父菜单key
    if (arr.length > 2) {
      menuState.openedFaterMenuKeys = [];
      menuState.openedFaterMenuKeys.push('/' + arr[1]);
      menuState.selectedMenuKeys = [];
      menuState.selectedMenuKeys.push(arr[2]);
    } else {
      // 如果没有父菜单
      if ('/home' === vueRoute.path) {
        // 如果是主页路由，则选中工作台菜单
        menuState.selectedMenuKeys.push('workbench');
      } else {
        menuState.openedFaterMenuKeys = [];
        menuState.selectedMenuKeys = [];
        menuState.selectedMenuKeys.push(arr[1]);
      }
    }

    // 刷新页面后，重新设置顶部的菜单名称
    if (sidebarRouters.value && Array.isArray(sidebarRouters.value)) {
      sidebarRouters.value.forEach((element) => {
        if (element.path === '/' + arr[1]) {
          headerInfo.currentFatherMenuTitle = element.meta?.title || '主页';
          if (element.children) {
            element.children.forEach((child) => {
              if (child.path === arr[2]) {
                headerInfo.currentMenuTitle = child.meta?.title || '';
              }
            });
          }
        }
      });
    }
  }
};

// 展开菜单时，收起其他已展开的菜单
const expandMenu = (openedFaterMenuKeys) => {
  const latestOpenKey = openedFaterMenuKeys.find((key) => menuState.openedFaterMenuKeys.indexOf(key) === -1);
  if (menuState.rootSubmenuKeys.indexOf(latestOpenKey) === -1) {
    menuState.openedFaterMenuKeys = openedFaterMenuKeys;
  } else {
    menuState.openedFaterMenuKeys = latestOpenKey ? [latestOpenKey] : [];
  }
};

// 添加错误边界处理
const handleError = (error) => {
  console.error('页面发生错误:', error);
  message.error('操作失败，请稍后重试');
};

// 添加导航守卫
const handleRouteChange = (to) => {
  // 检查路由是否存在
  if (to.path === '/home' || to.path === '/login' || to.path === '/') {
    return true;
  }

  // 检查是否是有效的子路由
  const isValidRoute = userRouters.some(route => {
    if (route.path === to.path) return true;
    if (route.children) {
      return route.children.some(child => child.path === to.path);
    }
    return false;
  });

  if (!isValidRoute) {
    message.error('页面不存在');
    return false;
  }
  return true;
};

// 处理菜单选择
const handleMenuSelect = (menuInfo) => {
  try {
    // 如果没有子路径，说明是父菜单，不进行导航
    if (!menuInfo.path) {
      return;
    }

    if (menuInfo.greatFatherPath) {
      // 处理三级菜单
      clickThreeLevelMenuItem(
        menuInfo.greatFatherPath,
        menuInfo.father.name,
        menuInfo.father.path,
        menuInfo.father.meta.title,
        menuInfo.menu.name,
        menuInfo.menu.path,
        menuInfo.menu.meta.title,
        menuInfo.menu.component
      );
    } else {
      // 处理二级菜单
      clickMenuItem(
        menuInfo.fatherName,
        menuInfo.fatherPath,
        menuInfo.fatherTitle,
        menuInfo.name,
        menuInfo.path,
        menuInfo.title,
        menuInfo.component
      );
    }
  } catch (error) {
    console.error('菜单选择错误:', error);
    message.error('菜单选择失败，请重试');
  }
};

// 优化路由跳转错误处理
const clickMenuItem = async (fatherMenuName, fatherMenuPath, fatherTitle, menuName, menuPath, menuTitle, menuComponent) => {
  try {
    loading.value = true;
    // 确保路径有效
    if (!fatherMenuPath || !menuPath) {
      throw new Error('无效的菜单路径');
    }

    if (menuPath === 'workbench') {
      //如果点击的是工作台菜单，则关闭其他已展开的父菜单
      menuState.openedFaterMenuKeys = [];
      await vueRouter.push({
        path: '/home/<USER>',
        meta: { title: '工作台' }
      });
    } else {
      let routePathStr = fatherMenuPath + '/' + menuPath;
      console.log('点击菜单后请求路由=' + routePathStr);
      await vueRouter.push({
        path: routePathStr,
        meta: { title: menuTitle }
      });

      headerInfo.currentFatherMenuTitle = fatherTitle;
      headerInfo.currentMenuTitle = menuTitle;
    }
  } catch (error) {
    handleError(error);
  } finally {
    loading.value = false;
  }
};

// 点击菜单项：跳转到对应的路由
const clickThreeLevelMenuItem = async (greatFatherMenuPath, fatherMenuName, fatherMenuPath, fatherTitle, menuName, menuPath, menuTitle, menuComponent) => {
  try {
    loading.value = true;
    // 确保路径有效
    if (!greatFatherMenuPath || !fatherMenuPath || !menuPath) {
      throw new Error('无效的菜单路径');
    }

    if (menuPath === 'workbench') {
      //如果点击的是工作台菜单，则关闭其他已展开的父菜单
      menuState.openedFaterMenuKeys = [];
    }
    headerInfo.currentFatherMenuTitle = fatherTitle;
    headerInfo.currentMenuTitle = menuTitle;

    let routePathStr = greatFatherMenuPath + '/' + fatherMenuPath + '/' + menuPath;
    console.log('点击菜单后请求路由=' + routePathStr);
    await vueRouter.push({
      path: routePathStr,
      meta: { title: menuTitle }
    });
  } catch (error) {
    handleError(error);
  } finally {
    loading.value = false;
  }
};

// 显示设置抽屉
const showDrawer = () => {
  drawerVisible.value = true;
};

// 关闭设置抽屉
const closeDrawer = () => {
  drawerVisible.value = false;
};

// 处理布局变更
const handleLayoutChange = () => {
  loading.value = true;
  appStore.updateSettings({
    navMode: layoutSettings.value.navMode
  }).then(() => {
    loading.value = false;
    message.success('布局设置已更新');
  });
};

// 处理主题变更
const handleThemeChange = () => {
  loading.value = true;
  nextTick(() => {
    // 添加过渡动画类
    document.body.classList.add('theme-transition');
    document.documentElement.classList.add('theme-transition');

    // 设置主题
    if (layoutSettings.value.darkMode) {
      document.body.classList.add('dark-theme');
      document.documentElement.classList.add('dark-theme');
    } else {
      document.body.classList.remove('dark-theme');
      document.documentElement.classList.remove('dark-theme');
    }

    // 强制重新计算主题变量
    themeVars.value;

    // 等待过渡动画完成后移除过渡类
    setTimeout(() => {
      document.body.classList.remove('theme-transition');
      document.documentElement.classList.remove('theme-transition');
      loading.value = false;
      message.success('主题设置已更新');
    }, 600);
  });
};

// 优化的退出登录处理
const handleLogout = async () => {
  try {
    loading.value = true;
    await userStore.logout();
    message.success('退出登录成功');
    vueRouter.push({ path: '/' });
  } catch (error) {
    message.error('退出登录失败');
  } finally {
    loading.value = false;
  }
};

// 进入个人中心
const enterPersonalCenter = () => {
  vueRouter.push({ path: '/system/user/profile' });
};

// 初始化加载
onMounted(() => {
  // 应用主题设置
  appStore.applyTheme();

  // 立即应用主题变量
  nextTick(() => {
    // 触发 themeVars 计算
    themeVars.value;

    // 设置初始主题
    if (layoutSettings.value.darkMode) {
      document.documentElement.classList.add('dark-theme');
    }

    // 设置系统标题
    if (appStore.systemConfig.title) {
      document.title = appStore.systemConfig.title;
    }
  });

  // 添加全局导航守卫
  vueRouter.beforeEach((to, from, next) => {
    if (handleRouteChange(to)) {
      next();
    } else {
      next('/404');
    }
  });

  initRootMenus();
  initRouteState();
});

// 监听布局设置变化并保存
watch(layoutSettings, (newSettings) => {
  localStorage.setItem('layoutSettings', JSON.stringify(newSettings));

  // 强制重新计算主题变量
  nextTick(() => {
    themeVars.value;

    // 设置主题
    if (newSettings.darkMode) {
      document.documentElement.classList.add('dark-theme');
      document.body.classList.add('dark-theme');
    } else {
      document.documentElement.classList.remove('dark-theme');
      document.body.classList.remove('dark-theme');
    }
  });
}, { deep: true });

// 处理设置变更
const handleSettingsChange = (newSettings) => {
  appStore.updateSettings(newSettings);

  // 立即应用主题变量
  nextTick(() => {
    // 触发 themeVars 计算
    themeVars.value;

    // 设置主题
    if (newSettings.darkMode) {
      document.documentElement.classList.add('dark-theme');
      document.body.classList.add('dark-theme');
    } else {
      document.documentElement.classList.remove('dark-theme');
      document.body.classList.remove('dark-theme');
    }
  });
};
// 刷新当前页面内容
const refreshCurrentPage = async () => {
  try {
    loading.value = true;

    // 使用provide/inject方式刷新页面，避免路由重定向
    // 生成一个随机key，强制router-view重新渲染
    const timestamp = new Date().getTime();
    // 保存当前滚动位置
    const scrollPosition = document.documentElement.scrollTop || document.body.scrollTop;

    // 强制重新获取路由数据
    if (vueRoute.meta && vueRoute.meta.keepAlive === false) {
      // 如果页面配置了不缓存，则重新加载数据
      const currentComponent = vueRoute.matched[vueRoute.matched.length - 1].instances.default;
      if (currentComponent && typeof currentComponent.fetchData === 'function') {
        await currentComponent.fetchData();
      }
    } else {
      // 通过修改查询参数的方式刷新页面
      const { fullPath, query } = vueRoute;
      const newQuery = { ...query, _refresh: timestamp };
      await vueRouter.replace({ path: vueRoute.path, query: newQuery });
    }

    // 恢复滚动位置
    nextTick(() => {
      window.scrollTo(0, scrollPosition);
    });

    message.success('页面刷新成功');
  } catch (error) {
    console.error('页面刷新失败:', error);
    message.error('页面刷新失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 替换 mapState 和 mapGetters
const sidebar = computed(() => appStore.sidebar);
const device = computed(() => appStore.device);
const avatar = computed(() => userStore.avatar);
const name = computed(() => userStore.name);
const roles = computed(() => userStore.roles);
const permissions = computed(() => userStore.permissions);
const routes = computed(() => permissionStore.routes);
const visitedViews = computed(() => tagsViewStore.visitedViews);
const cachedViews = computed(() => tagsViewStore.cachedViews);
</script>

<style lang="less" scoped>
.app-container {
  height: 100vh;
  .layout-container {
    height: 100%;
    background: #fafbfc;

    &__content {
      margin: 16px;

      .content-wrapper {
        background: #fff;
        border-radius: 16px;
        min-height: calc(100vh - 170px);
        padding: 16px;
      }
    }
  }
}
</style>

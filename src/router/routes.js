import BaseLayout from '@/layout/BaseLayout.vue';
import LoginPage from '@/views/LoginPage.vue';
import LoginPage2 from '@/views/LoginPage2.vue';
import LoginPage3 from '@/views/LoginPage3.vue';
import RegisterPage from '@/views/RegisterPage.vue';
import Profile from '@/views/system/user/profile/index.vue';
import frontendRoutes from './frontend';
import { createWebHistory, createRouter } from 'vue-router'
// 公共路由
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/LoginPage.vue'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/404.vue'),
    hidden: true
  },
//   {
//     path: '/401',
//     component: () => import('@/views/error/401.vue'),
//     hidden: true
//   },
  {
    path: '/',
    component: LoginPage,
  },
  {
    path: '/login',
    name: 'LoginPage',
    component: LoginPage,
  },
  {
    path: '/login2',
    name: 'LoginPage2',
    component: LoginPage2,
  },
  {
    path: '/login3',
    name: 'LoginPage3',
    component: LoginPage3,
  },
  {
    path: '/register',
    name: 'RegisterPage',
    component: RegisterPage,
  },
  {
    path: '/home',
    name: 'HomePage',
    component: BaseLayout,
    children: [
      {
        name: 'Workbench',
        path: 'workbench',
        component: () => import('@/views/workbench/WorkbenchPage.vue'),
      },
      {
        path: '',
        component: () => import('@/views/workbench/WorkbenchPage.vue'),
      }
    ],
  },
  {
    path: '/system/user/profile',
    name: 'profile',
    component: BaseLayout,
    children: [
      {
        path: '',
        component: Profile,
      }
    ],

  },
  // 前台路由
  ...frontendRoutes
];
const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }
    return { top: 0 }
  },
});

export default router;
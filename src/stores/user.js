import { defineStore } from 'pinia';
import { login, logout, getInfo } from '@/api/login';
import { getToken, setToken, removeToken } from '@/utils/auth';

export const useUserStore = defineStore('user', {
  state: () => ({
    token: getToken(),
    name: '',
    avatar: '',
    roles: [],
    permissions: [],
    nickName: ''
  }),

  actions: {
    // 登录
    async login(loginInfo) {
      try {
        const res = await login({
          username: loginInfo.username,
          password: loginInfo.password,
          code: loginInfo.code,
          uuid: loginInfo.uuid
        });
        setToken(res.token);
        this.token = res.token;
      } catch (error) {
        throw error;
      }
    },

    // 获取用户信息
    async getInfo() {
      try {
        const res = await getInfo();
        this.roles = res.roles;
        this.name = res.user.userName;
        this.avatar = res.user.avatar;
        this.permissions = res.permissions;
        this.nickName = res.user.nickName;
        return res;
      } catch (error) {
        throw error;
      }
    },

    // 退出系统
    async logout() {
      try {
        await logout();
        this.token = '';
        this.roles = [];
        this.permissions = [];
        removeToken();
      } catch (error) {
        throw error;
      }
    },

    // 前端 登出
    fedLogout() {
      this.token = '';
      removeToken();
    }
  }
});
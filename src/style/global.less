// 引入主题和组件样式
@import './theme.less';
@import './components/menu.less';
@import './components/button.less';

// 全局样式覆盖
.ant-layout {
  background: var(--bg-color);
}

.ant-card {
  background: var(--component-background);
  color: var(--text-color);
}

.ant-table {
  background: var(--component-background);
  color: var(--text-color);
  border-radius: 8px;
}

.ant-modal-content {
  background: var(--component-background);
}

.ant-modal-header {
  background: var(--component-background);
  border-bottom-color: var(--border-color-split);
}

.ant-modal-title {
  color: var(--text-color);
}

.ant-modal-footer {
  border-top-color: var(--border-color-split);
}

.ant-form-item-label > label {
  color: var(--text-color);
}

.ant-form {
  margin-bottom: 12px;
}

.ant-input,
.ant-input-number,
.ant-select-selector,
.ant-picker {
  background-color: var(--component-background) !important;
  border-color: var(--border-color-base) !important;
  color: var(--text-color) !important;
}

// 抽屉组件样式
.ant-drawer {
  .ant-drawer-content {
    background-color: var(--component-background);
  }

  .ant-drawer-header {
    background-color: var(--component-background);
    border-bottom-color: var(--border-color-split);
  }

  .ant-drawer-title {
    color: var(--text-color);
  }

  .ant-drawer-body {
    color: var(--text-color);
  }
}

// 全局文本颜色
body {
  color: var(--text-color);
}

// 按钮 hover 样式
.ant-btn:hover,
.ant-btn:focus {
  color: var(--ant-primary-color);
}

//.ant-btn-primary:hover,
//.ant-btn-primary:focus {
//  color: #fff;
//  background: var(--ant-primary-color);
//}

// 链接按钮 hover 样式
a:hover {
  color: var(--ant-primary-color);
}

// 菜单样式
.ant-menu-light {
  .ant-menu-item-selected {
    background-color: var(--ant-primary-1);
    color: var(--ant-primary-color);
  }

  .ant-menu-submenu-selected > .ant-menu-submenu-title {
    color: var(--ant-primary-color);
  }

  .ant-menu-item:hover,
  .ant-menu-item-active,
  .ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open,
  .ant-menu-submenu-active,
  .ant-menu-submenu-title:hover {
    color: var(--ant-primary-color);
  }
}

// 菜单图标样式
.ant-menu-item .anticon,
.ant-menu-submenu-title .anticon {
  color: inherit;
}

// 表格样式增强
.ant-table-thead > tr > th {
  font-weight: 600 !important;
  background-color: #f5f7fa !important;
}

.ant-table {
  border: 1px solid #f0f0f0;
  border-radius: 8px;

  &.ant-table-small {
    font-size: 14px;

    .ant-table-thead > tr > th {
      background-color: #dce5e9 !important;
    }
  }

  // 斑马条纹样式
  &.bearjia-table-striped {
    .ant-table-tbody > tr:nth-child(even) > td {
      background-color: rgba(0, 0, 0, 0.02);
    }

    .ant-table-tbody > tr:nth-child(even):hover > td {
      background-color: rgba(0, 0, 0, 0.04);
    }
  }

  // 表格尺寸样式
  &.bearjia-table-large {
    .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td {
      padding: 16px;
    }
  }

  &.bearjia-table-middle {
    .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td {
      padding: 12px 16px;
    }
  }

  &.bearjia-table-small {
    .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td {
      padding: 8px 16px;
    }
  }
}

// 表单样式增强
.autoee-detail-page .ant-form-item-label {
  font-weight: bold !important;
}

.autoee-form-whole-row {
  padding-left: 12px !important;
  padding-right: 36px !important;
}

.ant-input-number {
  width: 100% !important;
}

.ant-picker {
  width: 100% !important;
}

// 按钮样式增强
.ant-btn {
  margin-left: 5px;
  margin-right: 5px;
  border-radius: 8px;
}

.ant-btn-primary {
  color: #fff;
  background-color: var(--primary-color);

  &:hover {
    background-color: var(--primary-color);
  }

  &:active {
    color: #fff;
    background-color: var(--ant-primary-color-active);
  }

  &[disabled],
  &[disabled]:hover,
  &[disabled]:focus,
  &[disabled]:active {
    color: rgba(0, 0, 0, 0.25);
    background-color: #f5f5f5;
  }
}

.ant-btn:not(.ant-btn-primary):not(.ant-btn-link):hover {
  color: var(--primary-color);
}

// 工具类样式
.autoee-button-divider {
  height: 25px !important;
  border-color: rgb(76, 20, 218) !important;
  margin-left: 20px !important;
  margin-right: 20px !important;
}

.autoee-button-row {
  padding-bottom: 24px;
  margin-bottom: 12px!important;
}

// 主题切换过渡动画
.theme-transition {
  transition: background-color 0.5s ease, color 0.5s ease, border-color 0.5s ease, box-shadow 0.5s ease;

  * {
    transition: background-color 0.5s ease, color 0.5s ease, border-color 0.5s ease, box-shadow 0.5s ease;
  }
}

// 弹出框按钮样式
.ant-modal-confirm {
  .ant-btn-primary {
    color: #fff;
    background-color: var(--primary-color);

    &:hover {
      color: #fff;
      background-color: var(--ant-primary-color-hover);
    }

    &:active {
      color: #fff;
      background-color: var(--ant-primary-color-active);
    }
  }
}

// 确保所有主要按钮都有正确的样式
:deep(.ant-btn-primary) {
  color: #fff;
  background-color: var(--primary-color);
}

// 暗色主题样式
.dark-theme {
  // 表格样式
  .ant-table {
    background: var(--component-background);

    .ant-table-thead > tr > th {
      background: #1d1d1d;
      color: var(--text-color);
      border-bottom: 1px solid var(--border-color-split);
    }

    .ant-table-tbody > tr > td {
      border-bottom: 1px solid var(--border-color-split);
    }

    .ant-table-tbody > tr:hover > td {
      background: rgba(255, 255, 255, 0.08);
    }

    // 暗色主题下的斑马条纹
    &.bearjia-table-striped {
      .ant-table-tbody > tr:nth-child(even) > td {
        background-color: rgba(255, 255, 255, 0.02);
      }

      .ant-table-tbody > tr:nth-child(even):hover > td {
        background-color: rgba(255, 255, 255, 0.06);
      }
    }
  }

  // 文本颜色
  .ant-typography,
  .ant-statistic-title,
  .ant-statistic-content,
  .ant-descriptions-item-label,
  .ant-descriptions-item-content,
  .ant-form-item-label > label,
  .ant-select-selection-item,
  .ant-select-item,
  .ant-checkbox + span,
  .ant-radio-wrapper,
  .ant-tabs-tab,
  .ant-empty-description,
  .ant-result-title,
  .ant-result-subtitle,
  .ant-drawer-title,
  .ant-modal-title,
  .ant-modal-confirm-title,
  .ant-modal-confirm-content,
  .ant-popover-inner-content,
  .ant-tooltip-inner,
  .ant-alert-message,
  .ant-alert-description,
  .ant-badge-count,
  .ant-list-item-meta-title,
  .ant-list-item-meta-description,
  .ant-card-meta-title,
  .ant-card-meta-description,
  .ant-collapse-header,
  .ant-collapse-content,
  .ant-comment-content-author-name,
  .ant-comment-content-detail,
  .ant-tree-node-content-wrapper,
  .ant-tree-title,
  .ant-timeline-item-label,
  .ant-timeline-item-content,
  .ant-steps-item-title,
  .ant-steps-item-description,
  .ant-breadcrumb-link,
  .ant-breadcrumb-separator,
  .ant-pagination-item a,
  .ant-pagination-prev a,
  .ant-pagination-next a,
  .ant-pagination-jump-prev a,
  .ant-pagination-jump-next a,
  .ant-transfer-list-header-title,
  .ant-transfer-list-content-item,
  .ant-tag,
  .ant-rate-text,
  .ant-slider-mark-text,
  .ant-calendar-date,
  .ant-calendar-month-panel-cell-content,
  .ant-calendar-year-panel-cell-content,
  .ant-calendar-decade-panel-cell-content,
  .ant-time-picker-panel-input,
  .ant-time-picker-panel-select li,
  .ant-mention-dropdown-menu-item,
  .ant-cascader-menu-item-content,
  .ant-upload-list-item-name,
  .ant-upload-list-item-card-actions,
  .ant-upload-drag-hint,
  .ant-progress-text,
  .ant-progress-status-text,
  .ant-anchor-link-title,
  .ant-back-top-inner,
  .ant-spin-text,
  .ant-spin-dot,
  .ant-divider-inner-text,
  .ant-divider-plain-text,
  .ant-affix,
  .ant-drawer-body,
  .ant-list-item {
    color: var(--text-color) !important;
  }

  // 下拉菜单和选择器
  .ant-select-dropdown,
  .ant-dropdown-menu {
    background-color: var(--component-background);

    .ant-select-item,
    .ant-dropdown-menu-item {
      color: var(--text-color);

      &:hover {
        background-color: rgba(255, 255, 255, 0.08);
      }

      &-selected {
        background-color: var(--primary-color);
        color: #fff;
      }
    }
  }

  // 开关组件
  .ant-switch {
    background-color: rgba(255, 255, 255, 0.2);

    &-checked {
      background-color: var(--primary-color);
    }
  }

  // 列表项
  .ant-list-item {
    color: var(--text-color);
  }
}
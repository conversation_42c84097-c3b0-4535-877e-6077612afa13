@import './theme.less';

// 暗色主题全局样式
.dark-theme {
  // 基础背景色
  body,
  .ant-layout,
  .ant-layout-header,
  .ant-layout-sider,
  .ant-layout-content,
  .ant-layout-footer {
    background-color: var(--bg-color) !important;
  }

  // 组件背景色
  .ant-card,
  .ant-table,
  .ant-drawer-content,
  .ant-drawer-header,
  .ant-modal-content,
  .ant-modal-header,
  .ant-dropdown-menu,
  .ant-select-dropdown,
  .ant-popover-inner,
  .ant-tooltip-inner,
  .ant-menu,
  .ant-tabs-content,
  .ant-tabs-nav,
  .ant-collapse-content,
  .ant-collapse-header {
    background-color: var(--component-background) !important;
  }

  // 边框颜色
  .ant-card,
  .ant-table,
  .ant-drawer-content,
  .ant-modal-content,
  .ant-input,
  .ant-select-selector,
  .ant-picker,
  .ant-btn:not(.ant-btn-primary),
  .ant-tabs-tab,
  .ant-tabs-nav::before,
  .ant-collapse-header,
  .ant-collapse-content,
  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    border-color: var(--border-color-base) !important;
  }

  // 文本颜色
  *:not(.ant-btn-primary):not(.ant-btn-link):not(.ant-btn-text):not(a):not(.anticon) {
    color: var(--text-color) !important;
  }

  // 输入框
  .ant-input,
  .ant-input-number-input,
  .ant-select-selection-item,
  .ant-select-selection-search-input,
  .ant-picker-input > input,
  .ant-input-affix-wrapper,
  .ant-input-number,
  .ant-picker {
    color: var(--text-color) !important;
    background-color: var(--component-background) !important;
    border-color: var(--border-color-base) !important;
  }

  // 表格样式
  .ant-table {
    background: var(--component-background) !important;

    .ant-table-thead > tr > th {
      background: #1d1d1d !important;
      color: var(--text-color) !important;
      border-bottom: 1px solid var(--border-color-split) !important;
    }

    .ant-table-tbody > tr > td {
      border-bottom: 1px solid var(--border-color-split) !important;
    }

    .ant-table-tbody > tr:hover > td {
      background: rgba(255, 255, 255, 0.08) !important;
    }
  }

  // 下拉菜单和选择器
  .ant-select-dropdown,
  .ant-dropdown-menu {
    background-color: var(--component-background) !important;

    .ant-select-item,
    .ant-dropdown-menu-item {
      color: var(--text-color) !important;

      &:hover {
        background-color: rgba(255, 255, 255, 0.08) !important;
      }

      &-selected {
        background-color: var(--primary-color) !important;
        color: #fff !important;
      }
    }
  }

  // 开关组件
  .ant-switch {
    background-color: rgba(255, 255, 255, 0.2) !important;

    &-checked {
      background-color: var(--primary-color) !important;
    }
  }

  // 分割线
  .ant-divider {
    border-top-color: var(--border-color-split) !important;

    &-vertical {
      border-left-color: var(--border-color-split) !important;
    }
  }

  // 菜单样式
  .ant-menu {
    background-color: var(--menu-bg-dark) !important;
    color: var(--menu-item-color-dark) !important;

    .ant-menu-item,
    .ant-menu-submenu-title {
      color: var(--menu-item-color-dark) !important;

      &:hover {
        color: var(--menu-item-hover-color-dark) !important;
      }

      .anticon {
        color: inherit !important;
      }
    }

    .ant-menu-item-selected {
      background-color: var(--primary-color) !important;
      color: #fff !important;

      .anticon {
        color: #fff !important;
      }
    }

    .ant-menu-submenu-selected > .ant-menu-submenu-title {
      color: var(--menu-item-hover-color-dark) !important;
    }
  }

  // 按钮样式
  .ant-btn:not(.ant-btn-primary) {
    background-color: rgba(255, 255, 255, 0.04) !important;
    border-color: var(--border-color-base) !important;
    color: var(--text-color) !important;

    &:hover, &:focus {
      // 移除暗色主题下悬浮时的边框和背景颜色变化
      border-color: transparent !important;
      background-color: transparent !important;
      box-shadow: none !important;
    }
  }

  .ant-btn-primary {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;

    &:hover, &:focus, &:active {
      // 保持主要按钮的原始样式，不改变背景色
      background-color: var(--primary-color) !important;
      border-color: var(--primary-color) !important;
      box-shadow: none !important;
      transform: none !important;
    }
  }

  // 设置抽屉特殊样式
  .theme-setting-drawer {
    .ant-drawer-content {
      background-color: var(--component-background) !important;
    }

    .ant-drawer-header {
      background-color: var(--component-background) !important;
      border-bottom-color: var(--border-color-split) !important;
    }

    .ant-drawer-title {
      color: var(--text-color) !important;
    }

    .ant-drawer-body {
      color: var(--text-color) !important;
    }

    .setting-drawer-block h3 {
      color: var(--text-color) !important;
    }

    .ant-list-item {
      color: var(--text-color) !important;
    }
  }
}
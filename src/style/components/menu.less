@import '../theme.less';

// 侧边栏菜单样式
.side-menu {
  height: 100%;
  border-right: 0;
  
  // 自定义滚动条
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.12);
    border-radius: 3px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
    
    &:hover {
      background: rgba(0, 0, 0, 0.24);
    }
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.06);
    border-radius: 3px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
  }
}

// Logo区域样式
.layout-logo {
  height: 64px;
  line-height: 64px;
  overflow: hidden;
  white-space: nowrap;
  // background: var(--menu-bg-dark);
  padding: 0 16px;
  
  img {
    width: 32px;
    height: 32px;
    margin-right: 12px;
    vertical-align: middle;
  }
  
  h1 {
    display: inline-block;
    margin: 0;
    color: #fff;
    font-weight: 600;
    font-size: 18px;
    vertical-align: middle;
  }
}

// 亮色主题菜单样式
.ant-menu-light {
  background: var(--menu-bg-light);
  
  .ant-menu-item {
    color: var(--menu-item-color-light);
    transition: all 0.3s;
    
    &:hover {
      color: var(--menu-item-hover-color-light) !important;
      background: transparent;
    }
    
    &.ant-menu-item-selected {
      color: var(--menu-item-active-color-light) !important;
      background: var(--menu-item-active-bg-light) !important;
      
      &::after {
        border-right-color: var(--primary-color) !important;
      }
      
      .anticon {
        color: var(--menu-item-active-color-light) !important;
      }
    }
    
    .anticon {
      color: inherit;
    }
  }
  
  .ant-menu-submenu-title {
    color: var(--menu-item-color-light);
    transition: all 0.3s;
    
    &:hover {
      color: var(--menu-item-hover-color-light) !important;
      background: transparent;
    }
    
    .anticon {
      color: inherit;
    }
  }
  
  .ant-menu-submenu-selected > .ant-menu-submenu-title {
    color: var(--menu-item-active-color-light) !important;
    
    .anticon {
      color: var(--menu-item-active-color-light) !important;
    }
  }
}

// 暗色主题菜单样式
.dark-theme {
  .side-menu {
    background: var(--menu-bg-dark);
  }
  
  .ant-menu-dark {
    background: var(--menu-bg-dark);
    
    .ant-menu-item {
      color: var(--menu-item-color-dark);
      transition: all 0.3s;
      
      &:hover {
        color: var(--menu-item-hover-color-dark) !important;
        background: var(--primary-color);
      }
      
      &.ant-menu-item-selected {
        color: var(--menu-item-active-color-dark) !important;
        background: var(--primary-color) !important;
        
        .anticon {
          color: var(--menu-item-active-color-dark) !important;
        }
      }
      
      .anticon {
        color: inherit;
      }
    }
    
    .ant-menu-submenu-title {
      color: var(--menu-item-color-dark);
      transition: all 0.3s;
      
      &:hover {
        color: var(--menu-item-hover-color-dark) !important;
        background: var(--primary-color);
      }
      
      .anticon {
        color: inherit;
      }
    }
    
    .ant-menu-submenu-selected > .ant-menu-submenu-title {
      color: var(--menu-item-active-color-dark) !important;
      
      .anticon {
        color: var(--menu-item-active-color-dark) !important;
      }
    }
  }
}
<template>
  <div>
    <a-row :gutter="16">
      <a-col span="6">
        <!-- 部门树 -->
        <dept-tree
          ref="deptTreeRef"
          :dept-options="deptOptions"
          @select="clickDeptNode"
        />
      </a-col>
      <a-col span="18">
        <a-form ref="queryFormRef" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }">
          <a-row :gutter="24">
            <a-col span="8">
              <a-form-item name="userName" label="用户账号">
                <a-input v-model:value="queryFormObj.data.userName" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="8">
              <a-form-item name="nickName" label="用户名称">
                <a-input v-model:value="queryFormObj.data.nickName" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="8">
              <a-form-item name="sex" label="用户性别">
                <a-select
                  v-model:value="queryFormObj.data.sex"
                  :options="pageDataObj.sysUserSexDict"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col span="8">
              <a-form-item name="status" label="帐号状态">
                <a-select
                  v-model:value="queryFormObj.data.status"
                  :options="pageDataObj.sysNormalDisableDict"
                  allow-clear
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24" class="autoee-button-row">
            <a-col :span="12" style="text-align: left">
              <a-button type="primary" @click="openAddModal" v-has-permi="['system:user:add']">
                <BearJiaIcon icon="plus-outlined" />新增
              </a-button>
              <a-button
                type="primary"
                danger
                @click="clickDelete"
                :disabled="tableObj.selectedRowKeys.length <= 0"
                v-has-permi="['system:user:remove']"
              >
                <BearJiaIcon icon="delete-outlined" />删除
              </a-button>
              <a-button type="primary" @click="clickExport" v-has-permi="['system:user:export']">
                <BearJiaIcon icon="export-outlined" />导出
              </a-button>
            </a-col>
            <a-col :span="12" style="text-align: right">
              <a-button type="primary" @click="queryTableData">
                <BearJiaIcon icon="SearchOutlined" />查询
              </a-button>
              <a-button @click="resetQueryForm">
                <BearJiaIcon icon="redo-outlined" />重置
              </a-button>
            </a-col>
          </a-row>
        </a-form>

        <a-table
          row-key="userId"
          :columns="tableObj.columns"
          :data-source="tableObj.dataSource"
          :loading="tableObj.loading"
          :pagination="tablePagination"
          @change="tableHandChangePage"
          :row-selection="{ selectedRowKeys: tableObj.selectedRowKeys, onChange: onTableSelectChange }"
        >
          <template #bodyCell="{ index, column, record }">
            <template v-if="column.key === 'pageIndex'">
              {{ index + 1 }}
            </template>
            <template v-else-if="column.key === 'deptId'">
              <span>
                {{ BearJiaUtil.getDictLabelByKey(record.deptId, pageDataObj.deptIdDict) }}
              </span>
            </template>
            <template v-else-if="column.key === 'sex'">
              <span>
                {{ BearJiaUtil.getDictLabelByKey(record.sex, pageDataObj.sysUserSexDict) }}
              </span>
            </template>
            <template v-else-if="column.key === 'status'">
              <span>
                <a-badge status="success" />
                {{ BearJiaUtil.getDictLabelByKey(record.status, pageDataObj.sysNormalDisableDict) }}
              </span>
            </template>
            <template v-else-if="column.key === 'userTableOperateCol'">
              <span>
                <a @click="openDetailModal(record)">查看</a>
                <a-divider type="vertical" v-has-permi="['system:user:edit']" />
                <a @click="openUpdateModal(record)" v-has-permi="['system:user:edit']">修改</a>
                <a-divider type="vertical" v-has-permi="['system:user:edit']" />
                <a @click="openResetPasswordModal(record)">重置密码</a>
              </span>
            </template>
          </template>
        </a-table>
      </a-col>
    </a-row>

    <AddUpdateModal
      ref="addUpdateModalRef"
      @refresh-father-page-table="queryTableData"
      :dept-id-dict="pageDataObj.deptIdDict"
      :user-type-dict="pageDataObj.userTypeDict"
      :sys-user-sex-dict="pageDataObj.sysUserSexDict"
      :sys-normal-disable-dict="pageDataObj.sysNormalDisableDict"
    />
    <DetailModal
      ref="detailModalRef"
      :dept-id-dict="pageDataObj.deptIdDict"
      :user-type-dict="pageDataObj.userTypeDict"
      :sys-user-sex-dict="pageDataObj.sysUserSexDict"
      :sys-normal-disable-dict="pageDataObj.sysNormalDisableDict"
    />
    <UseResetPassword ref="useResetPasswordRef" />
  </div>
</template>

<script setup>
import { listUser, delUser } from '@/api/system/user';
import AddUpdateModal from './addUpdateModal.vue';
import DetailModal from './detailModal.vue';
import UseResetPassword from './useResetPassword.vue';
import DeptTree from './DeptTree.vue';
import { computed, reactive, ref } from 'vue';
import { BearJiaIcon } from '@/utils/BearJiaIcon.js';
import BearJiaUtil from '@/utils/BearJiaUtil.js';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { useUserStore } from '@/stores/user';
import { useAppStore } from '@/stores/app';
import { usePermissionStore } from '@/stores/permission';
import { useTableConfigStore } from '@/stores/tableConfig';

// 获取 store
const userStore = useUserStore();
const appStore = useAppStore();
const permissionStore = usePermissionStore();
const tableConfigStore = useTableConfigStore();

// 当前页面使用的数据
const pageDataObj = reactive({
  deptTreeData: [],
  deptIdDict: [], // 添加 deptIdDict 用于部门字典
  userTypeDict: [],
  sysUserSexDict: [],
  sysNormalDisableDict: [],
});

// 异步加载数据
BearJiaUtil.getDeptTreeData().then((res) => {
  pageDataObj.deptTreeData = res.data;
  // 假设 deptIdDict 需要从 deptTreeData 中生成
  pageDataObj.deptIdDict = res.data.map(item => ({ value: item.id, label: item.label }));
});
pageDataObj.userTypeDict = [{ value: 'v1', label: '自定义1' }, { value: 'v2', label: '自定义2' }];
BearJiaUtil.getDictsByType('sys_user_sex').then((res) => {
  pageDataObj.sysUserSexDict = res;
});
BearJiaUtil.getDictsByType('sys_normal_disable').then((res) => {
  pageDataObj.sysNormalDisableDict = res;
});

// 查询 Form
const queryFormRef = ref();
const queryFormObj = reactive({
  data: {
    pageNum: 1,
    pageSize: 10,
    deptId: null,
    userName: null,
    nickName: null,
    sex: null,
    status: null,
    params: {},
  },
});

// 重置查询 Form
const resetQueryForm = () => {
  queryFormObj.data = {
    pageNum: 1,
    pageSize: 10,
    deptId: null,
    userName: null,
    nickName: null,
    sex: null,
    status: null,
    params: {},
  };
  queryTableData();
};

// 部门树选项（从 pageDataObj 获取）
const deptOptions = computed(() => pageDataObj.deptTreeData);

// 点击部门节点
const clickDeptNode = (keys) => {
  if (keys.length > 0) {
    queryFormObj.data.deptId = keys[0];
    queryTableData();
  }
};

// 表格定义
const tableObj = reactive({
  dataSource: [],
  total: 0,
  loading: false,
  selectedRowKeys: [],
  columns: [
    { title: '序号', dataIndex: 'pageIndex', key: 'pageIndex', width: 80, align: 'center', resizable: true },
    { title: '用户账号', key: 'userName', dataIndex: 'userName', width: 120, resizable: true },
    { title: '用户名称', key: 'nickName', dataIndex: 'nickName', width: 120, resizable: true },
    { title: '手机号码', key: 'phonenumber', dataIndex: 'phonenumber', width: 130, resizable: true },
    { title: '用户性别', key: 'sex', dataIndex: 'sex', width: 100, resizable: true },
    { title: '帐号状态', key: 'status', dataIndex: 'status', width: 100, resizable: true },
    { title: '操作', key: 'userTableOperateCol', width: 180, resizable: true },
  ],
});

// 查询表格数据
const queryTableData = () => {
  BearJiaUtil.getTableDataByQueryFunc(listUser(queryFormObj.data), tableObj);
};

// 初始化查询
queryTableData();

// 分页配置 - 使用全局配置
const tablePagination = computed(() => {
  const defaultPagination = BearJiaUtil.createTablePagination(tableObj, queryFormObj);
  return {
    ...tableConfigStore.pagination,
    ...defaultPagination,
    // 保留必要的数据绑定
    total: tableObj.total,
    current: queryFormObj.data.pageNum,
    pageSize: queryFormObj.data.pageSize,
  };
});

// 手动翻页
const tableHandChangePage = (page) => {
  queryFormObj.data.pageSize = page.pageSize;
  queryFormObj.data.pageNum = page.current;
  queryTableData();
};

// 表格行选择
const onTableSelectChange = (selectedRowKeys) => {
  tableObj.selectedRowKeys = selectedRowKeys;
};

// 组件引用
const addUpdateModalRef = ref();
const detailModalRef = ref();
const useResetPasswordRef = ref();
const deptTreeRef = ref();

// 打开新增窗口
const openAddModal = () => {
  addUpdateModalRef.value.openAddModal();
};

// 打开修改窗口
const openUpdateModal = (record) => {
  addUpdateModalRef.value.openUpdateModal(record);
};

// 打开重置密码窗口
const openResetPasswordModal = (record) => {
  useResetPasswordRef.value.openModal(record);
};

// 打开详情窗口
const openDetailModal = (record) => {
  detailModalRef.value.openModal(record);
};

// 点击删除
const clickDelete = () => {
  BearJiaUtil.confirmDeleteSelectedData(() => {
    delUser(tableObj.selectedRowKeys).then((res) => {
      BearJiaUtil.messageSuccess('删除操作成功。');
      queryTableData(); // 更新表格数据
    });
  });
};

// 点击导出
const clickExport = () => {
  BearJiaUtil.download(
    'system/user/export',
    queryFormObj.data,
    `用户信息_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`
  );
};
</script>

<style lang="less" scoped>
/* 如果需要添加样式，可以在这里定义 */
</style>
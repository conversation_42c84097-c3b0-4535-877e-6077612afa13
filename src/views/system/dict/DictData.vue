<template>
  <div>
    <a-row :gutter="24" style="padding: 28px;">
      <a-col>
        <a-form ref="queryDataFormRef" name="queryDataForm" :model="queryDataForm.data">
          <a-row :gutter="24">
            <a-col span="8">
              <a-form-item name="dictValue" label="字典值">
                <a-input v-model:value="queryDataForm.data.dictValue" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="8">
              <a-form-item name="dictLabel" label="字典名称">
                <a-input v-model:value="queryDataForm.data.dictLabel" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="8">
              <a-form-item name="status" label="状态">
                <a-select
                    v-model:value="queryDataForm.data.status"
                    :options="pageData.sysNormalDisableDict"
                    allow-clear
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24" class="autoee-button-row">
            <a-col :span="12" style="text-align: left">
              <a-button type="primary" @click="openDataAddModal" v-has-permi="['system:dict:add']">
                <BearJiaIcon icon="plus-outlined" />新增
              </a-button>
              <a-button
                  type="primary"
                  danger
                  @click="clickDeleteData"
                  :disabled="dataTableData.selectedRowKeys.length <= 0"
                  v-has-permi="['system:dict:remove']"
              >
                <BearJiaIcon icon="delete-outlined" />删除
              </a-button>
              <a-button type="primary" @click="clickExportData" v-has-permi="['system:dict:export']">
                <BearJiaIcon icon="export-outlined" />导出
              </a-button>
            </a-col>
            <a-col :span="12" style="text-align: right">
              <a-button type="primary" @click="queryDataTableData">
                <BearJiaIcon icon="SearchOutlined" />查询
              </a-button>
              <a-button @click="resetDataQueryForm">
                <BearJiaIcon icon="redo-outlined" />重置
              </a-button>
            </a-col>
          </a-row>
        </a-form>

        <a-table
            row-key="dictCode"
            :columns="dataTableData.columns"
            :data-source="dataTableData.dataSource"
            :loading="dataTableData.loading"
            :pagination="dataTablePagination"
            @change="dataTableHandChangePage"
            :row-selection="{ selectedRowKeys: dataTableData.selectedRowKeys, onChange: onDataTableSelectChange }"
        >
          <template #bodyCell="{ index, column, record }">
            <template v-if="column.key === 'pageIndex'">
              {{ index + 1 }}
            </template>
            <template v-else-if="column.key === 'status'">
              <span>
                {{ BearJiaUtil.getDictLabelByKey(record.status, pageData.sysNormalDisableDict) }}
              </span>
            </template>
            <template v-else-if="column.key === 'dataTableOperateCol'">
              <span>
                <a @click="openDataDetailModal(record)">查看</a>
                <a-divider type="vertical" v-has-permi="['system:dict:edit']" />
                <a @click="openDataUpdateModal(record)" v-has-permi="['system:dict:edit']">修改</a>
              </span>
            </template>
          </template>
        </a-table>

        <DataAddUpdateModal
            ref="dataAddUpdateModalRef"
            :sys-yes-no-dict="pageData.sysYesNoDict"
            :sys-normal-disable-dict="pageData.sysNormalDisableDict"
            @refresh-father-data-table-data="queryDataTableData"
        />
        <DataDetailModal
            ref="dataDetailModalRef"
            :sys-yes-no-dict="pageData.sysYesNoDict"
            :sys-normal-disable-dict="pageData.sysNormalDisableDict"
            @refresh-father-data-table-data="queryDataTableData"
        />
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useUserStore } from '@/stores/user';
import { useAppStore } from '@/stores/app';
import { listData, delData } from '@/api/system/dict/data';
import DataAddUpdateModal from './dataAddUpdateModal.vue';
import DataDetailModal from './dataDetailModal.vue';
import { BearJiaIcon } from '@/utils/BearJiaIcon.js';
import BearJiaUtil from '@/utils/BearJiaUtil.js';
import { Modal, message } from 'ant-design-vue';

// 定义 Props
const props = defineProps({
  dictType: {
    type: String,
    required: true,
  },
  dictId: {
    type: Number,
    required: true,
  },
});

// 获取 store
const userStore = useUserStore();
const appStore = useAppStore();

// 替换 Vuex store 的使用
const user = computed(() => userStore);
const app = computed(() => appStore);

// 页面数据
const pageData = reactive({
  sysNormalDisableDict: [],
  sysYesNoDict: [], // 假设需要此字典
});

// 异步加载字典数据
BearJiaUtil.getDictsByType('sys_normal_disable').then((res) => {
  pageData.sysNormalDisableDict = res;
});
BearJiaUtil.getDictsByType('sys_yes_no').then((res) => {
  pageData.sysYesNoDict = res; // 如果需要 sys_yes_no
});

// 查询表单
const queryDataFormRef = ref();
const queryDataForm = reactive({
  data: {
    dictValue: null,
    dictLabel: null,
    status: null,
    dictType: props.dictType,
    pageNum: 1,
    pageSize: 10,
  },
});

// 重置查询表单
const resetDataQueryForm = () => {
  queryDataForm.data = {
    dictValue: null,
    dictLabel: null,
    status: null,
    dictType: props.dictType,
    pageNum: 1,
    pageSize: 10,
  };
  queryDataTableData();
};

// 表格数据
const dataTableData = reactive({
  dataSource: [],
  total: 0,
  loading: false,
  selectedRowKeys: [],
  columns: [
    { title: '序号', dataIndex: 'pageIndex', key: 'pageIndex', width: 50, align: 'center' },
    { title: '字典编码', dataIndex: 'dictCode', align: 'center' },
    { title: '字典标签', dataIndex: 'dictLabel', ellipsis: true, align: 'center' },
    { title: '字典键值', dataIndex: 'dictValue', ellipsis: true, align: 'center' },
    { title: '字典排序', dataIndex: 'dictSort', ellipsis: true, align: 'center' },
    { title: '状态', dataIndex: 'status', key: 'status', align: 'center' },
    { title: '备注', dataIndex: 'remark', ellipsis: true, align: 'center' },
    { title: '创建时间', dataIndex: 'createTime', ellipsis: true, align: 'center' },
    { title: '操作', dataIndex: 'operation', key: 'dataTableOperateCol', width: '15%', align: 'center' },
  ],
});

// 分页配置
const dataTablePagination = computed(() =>{
  // 当数据条数 ≤ 10 时禁用分页
  if (dataTableData.dataSource.length <= 10) {
    return false;
  }
  BearJiaUtil.createTablePagination(dataTableData, queryDataForm)
});

// 查询表格数据
const queryDataTableData = () => {
  dataTableData.loading = true;
  listData(queryDataForm.data).then((response) => {
    dataTableData.dataSource = response.rows;
    dataTableData.total = response.total;
    dataTableData.loading = false;
  });
};

// 初始化查询
onMounted(() => {
  queryDataTableData();
});

// 表格翻页
const dataTableHandChangePage = (page) => {
  queryDataForm.data.pageSize = page.pageSize;
  queryDataForm.data.pageNum = page.current;
  queryDataTableData();
};

// 表格行选择
const onDataTableSelectChange = (selectedRowKeys) => {
  dataTableData.selectedRowKeys = selectedRowKeys;
};

// 组件引用
const dataAddUpdateModalRef = ref();
const dataDetailModalRef = ref();

// 打开新增模态框
const openDataAddModal = () => {
  dataAddUpdateModalRef.value.openAddModal();
};

// 打开修改模态框
const openDataUpdateModal = (record) => {
  dataAddUpdateModalRef.value.openUpdateModal(record);
};

// 打开详情模态框
const openDataDetailModal = (record) => {
  dataDetailModalRef.value.openModal(record);
};

// 点击删除
const clickDeleteData = () => {
  const dictCodes = dataTableData.selectedRowKeys;
  Modal.confirm({
    title: '确认删除所选中数据?',
    content: `当前选中字典编码为 ${dictCodes.join(', ')} 的数据`,
    onOk() {
      return delData(dictCodes).then(() => {
        dataTableData.selectedRowKeys = [];
        queryDataTableData();
        message.success('删除成功');
        app.dispatch('dict/removeDict', props.dictType);
      });
    },
  });
};

// 点击导出（假设 BearJiaUtil.download 已实现）
const clickExportData = () => {
  BearJiaUtil.download(
      'system/dict/data/export',
      queryDataForm.data,
      `字典数据_${new Date().toLocaleString()}.xlsx`
  );
};
</script>

<style lang="less" scoped>
.autoee-button-row {
  margin-top: 16px;
}
</style>
import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';
import router from './router';
import Antd from 'ant-design-vue';
import directive from './directive';
import hasPermi from './directive/permission/hasPermi';
import VueHighlightJS from 'vue3-highlightjs';
import 'vue3-highlightjs/styles/solarized-light.css';
import 'ant-design-vue/dist/reset.css';
import ErrorHandler from './plugins/errorHandler';
import tablePlugin from '@/plugins/table';

// 导入全局样式
import './style/global.less';
// 导入暗色主题样式
import './style/dark-theme.less';

const app = createApp(App);
const pinia = createPinia();

app.use(pinia)
   .use(router)
   .use(Antd)
   .use(directive)
   .use(VueHighlightJS)
   .use(ErrorHandler)
   .use(tablePlugin);

// 挂载应用
app.mount('#app');

// 应用挂载后初始化配置
import { initAllConfigs, watchConfigChanges } from '@/utils/configInit';
setTimeout(() => {
  initAllConfigs();
  watchConfigChanges();
}, 100);

// 注册全局组件
import GlobalLoading from './components/common/GlobalLoading.vue';
app.component('GlobalLoading', GlobalLoading);

// 导入工具函数
import {
  parseTime,
  resetForm,
  addDateRange,
  selectDictLabel,
  selectDictLabels,
  handleTree
} from '@/utils/bearjia.js';
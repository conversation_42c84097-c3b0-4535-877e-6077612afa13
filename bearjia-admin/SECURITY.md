# 安全配置指南

## 配置说明

项目使用 `application.yml` 和 `application-druid.yml` 进行配置，支持通过环境变量覆盖敏感配置项。

## 生产环境配置

为了提高系统安全性，生产环境建议通过环境变量配置敏感信息：

### 1. 数据库配置
```bash
export DB_URL="******************************************************************************************************************************************************"
export DB_USERNAME="your_username"
export DB_PASSWORD="your_secure_password"
```

### 2. JWT 配置
```bash
export JWT_SECRET="your_very_secure_jwt_secret_key_minimum_32_characters"
```

### 3. Druid 监控配置
```bash
export DRUID_USERNAME="admin"
export DRUID_PASSWORD="your_secure_druid_password"
```

### 4. 文件上传配置
```bash
export UPLOAD_PATH="/path/to/your/upload"
```

### 5. 日志配置
```bash
export LOG_PATH="/path/to/your/logs"
```

## 安全建议

### 1. JWT 密钥
- 使用至少 32 位的复杂密钥
- 定期更换密钥
- 不要在代码中硬编码密钥

### 2. 数据库安全
- 使用专用数据库用户，避免使用 root
- 设置强密码
- 限制数据库访问权限

### 3. Druid 监控
- 生产环境建议禁用或限制访问
- 使用强密码
- 配置 IP 白名单

### 4. 生产环境配置
- 禁用 Swagger 文档
- 设置适当的日志级别
- 配置 HTTPS
- 启用防火墙

## 部署建议

### 开发环境
- 直接使用 `application.yml` 中的默认配置
- 根据需要修改配置文件中的值

### 生产环境
1. 设置上述环境变量
2. 使用系统环境变量或配置管理工具
3. 确保敏感信息不被提交到版本控制
4. 定期更换密钥和密码

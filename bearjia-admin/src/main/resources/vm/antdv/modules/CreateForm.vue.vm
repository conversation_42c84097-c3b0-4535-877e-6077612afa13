#set($isInsert=0)
#set($isEdit=0)
#set($hasRichText=0)
#foreach($column in $columns)
#if($column.insert)
#set($isInsert=1)
#end
#if($column.edit)
#set($isEdit=1)
#end
#if($isInsert == 1 && $isEdit == 1)
#break
#end
#end
#if($isInsert == 0 && $isEdit == 0)
<template>
  <a-result title="多余文件,请删除">
    <template #icon>
      <a-icon type="smile" theme="twoTone" />
    </template>
    <template #extra>
      <a-button type="primary" @click="returnIndex">
               返回首页
      </a-button>
    </template>
  </a-result>
</template>

#stop
#end
<template>
  <a-drawer width="35%" :label-col="4" :wrapper-col="14" :visible="open" @close="onClose">
    <a-divider orientation="left">
      <b>{{ formTitle }}</b>
    </a-divider>
    <a-form-model ref="form" :model="form" :rules="rules">
#set($requiredCount = 0)
#set($ruleLastIndex = 0)
#foreach($column in $columns)
#if(${column.dictType} != '')
#set($ruleLastIndex = $foreach.count)
#end
#set($field=$column.javaField)
#if(($column.insert || $column.edit) && !$column.pk)
#if(($column.usableColumn) || (!$column.superColumn))
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($column.required)
#set($requiredCount = $requiredCount + 1)
#end
#set($dictType=$column.dictType)
      <a-form-model-item label="${comment}" prop="${field}" #if($column.insert && !$column.edit)v-if="formType === 1"#end#if(!$column.insert && $column.edit)v-if="formType === 2"#end>
#if("" != $treeParentCode && $column.javaField == $treeParentCode)
        <a-tree-select
          v-model="form.${field}"
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :tree-data="${businessName}Options"
          placeholder="请选择"
          :replaceFields="{children:'children', title:'${treeName}', key:'${treeCode}', value: '${treeCode}' }"
          tree-default-expand-all
        >
        </a-tree-select>
#elseif($column.htmlType == "input")
        <a-input v-model="form.${field}" placeholder="请输入${comment}" />
#elseif($column.htmlType == "imageUpload")
        <file-upload v-model="form.${field}" type="image"></file-upload>
#elseif($column.htmlType == "fileUpload")
        <file-upload v-model="form.${field}" type="file"></file-upload>
#elseif($column.htmlType == "editor")
#set($hasRichText=1)
        <editor v-model="form.${field}" />
#elseif($column.htmlType == "select" && "" != $dictType)
        <a-select placeholder="请选择${comment}" v-model="form.${field}">
          <a-select-option v-for="(d, index) in ${field}Options" :key="index" :value="d.value" >{{ d.label }}</a-select-option>
        </a-select>
#elseif($column.htmlType == "select" && $dictType)
        <a-select placeholder="请选择${comment}" v-model="form.${field}">
          <a-select-option value="" >请选择字典生成</a-select-option>
        </a-select>
#elseif($column.htmlType == "checkbox" && "" != $dictType)
        <a-select
          mode="multiple"
          v-model="form.${field}"
          placeholder="请选择${comment}"
        >
          <a-select-option v-for="(d, index) in ${field}Options" :key="index" :value="d.value" >{{ d.label }}</a-select-option>
        </a-select>
#elseif($column.htmlType == "checkbox" && $dictType)
        <a-select placeholder="请选择${comment}" v-model="form.${field}">
          <a-select-option value="" >请选择字典生成</a-select-option>
        </a-select>
#elseif($column.htmlType == "radio" && "" != $dictType)
        <a-radio-group v-model="form.${field}" button-style="solid">
          <a-radio-button v-for="(d, index) in ${field}Options" :key="index" :value="d.value">{{ d.label }}</a-radio-button>
        </a-radio-group>
#elseif($column.htmlType == "radio" && $dictType)
        <a-radio-group v-model="form.${field}" button-style="solid">
          <a-radio-button value="">请选择字典生成</a-radio-button>
        </a-radio-group>
#elseif($column.htmlType == "datetime")
        <a-date-picker style="width: 100%" v-model="form.${field}" format="YYYY-MM-DD HH:mm:ss" allow-clear/>
#elseif($column.htmlType == "textarea")
        <a-input v-model="form.${field}" placeholder="请输入内容" type="textarea" allow-clear />
#end
      </a-form-model-item>
#end
#end
#end
#if($table.sub)
      <a-divider orientation="left">
        <b>${subTable.functionName}</b>
      </a-divider>
      <sub-table ref="subTable" :list="subList" @add="handleAdd${subclassName}" @delete="handleDelete${subclassName}" />
      <p>当前存在 {{ subList.length }} 条信息</p>
      <a-button type="dashed" @click="$refs.subTable.show()">
        详情
      </a-button>
      <br>
#end
      <div class="bottom-control">
        <a-space>
          <a-button type="primary" :loading="submitLoading" @click="submitForm">
            保存
          </a-button>
          <a-button type="dashed" @click="cancel">
            取消
          </a-button>
        </a-space>
      </div>
    </a-form-model>
  </a-drawer>
</template>

<script>
import { get${BusinessName}, add${BusinessName}, update${BusinessName} } from '@/api/${moduleName}/${businessName}'
#if($table.sub)
import SubTable from './SubTable'
#end
#if($hasRichText == 1)
import Editor from '@/components/Editor'
#end

export default {
  name: 'CreateForm',
  props: {
#set($propsCount = 0)
#foreach ($column in $columns)
#if(${column.dictType} != '')
#set($propsCount = 1)
    ${column.javaField}Options: {
      type: Array,
      required: true
#if($foreach.count!=$ruleLastIndex || $tplCategory == 'tree')
    },
#else
    }
#end
#end
#end
#if($tplCategory == 'tree')
    ${businessName}Options: {
      type: Array,
      required: true
    }
#end
  },
  components: {
#if($table.sub)
    SubTable#if($hasRichText == 1),#end
#end
#if($hasRichText == 1)
    Editor
#end
  },
  data () {
    return {
      submitLoading: false,
#if($table.sub)
      subList: [],
#end
      formTitle: '',
      // 表单参数
      form: {
#foreach ($column in $columns)
#if($column.insert || $column.edit)
#if($column.htmlType == "radio")
        $column.javaField: #if($column.javaType == "Integer" || $column.javaType == "Long")0#else'0'#end#if($foreach.count != $columns.size()),#end
#elseif($column.htmlType == "checkbox")
        $column.javaField: []#if($foreach.count != $columns.size()),#end
#else
        $column.javaField: null#if($foreach.count != $columns.size()),#end
#end

#end
#end
      },
      // 1增加,2修改
      formType: 1,
      open: false,
      rules: {
#set($currentRequiredCount = 1)
#foreach ($column in $columns)
#if(($column.insert || $column.edit) && !$column.pk)
#if(($column.usableColumn) || (!$column.superColumn))
#if($column.required)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
        $column.javaField: [
          { required: true, message: '$comment不能为空', trigger: #if($column.htmlType == "select")'change'#else'blur'#end }
        ]#if($currentRequiredCount != $requiredCount),#end

#set($currentRequiredCount = $currentRequiredCount + 1)
#end
#end
#end
#end
      }
    }
  },
  filters: {
  },
  created () {
  },
  computed: {
  },
  watch: {
  },
  mounted () {
  },
  methods: {
    onClose () {
      this.open = false
#if($table.sub)
      this.subList = []
#end
    },
    // 取消按钮
    cancel () {
      this.open = false
      this.reset()
#if($table.sub)
      this.subList = []
#end
    },
    // 表单重置
    reset () {
      this.formType = 1
      this.form = {
#foreach ($column in $columns)
#if($column.insert || $column.edit)
#if($column.htmlType == "radio")
        $column.javaField: #if($column.javaType == "Integer" || $column.javaType == "Long")0#else'0'#end#if($foreach.count != $columns.size()),#end
#elseif($column.htmlType == "checkbox")
        $column.javaField: []#if($foreach.count != $columns.size()),#end
#else
        $column.javaField: null#if($foreach.count != $columns.size()),#end
#end

#end
#end
      }
    },
    /** 新增按钮操作 */
    handleAdd (row) {
      this.reset()
#if($tplCategory == 'tree')
      this.$emit('select-tree')
      if (row != null && row.${treeCode}) {
        this.form.${treeParentCode} = row.${treeCode}
      } else {
        this.form.${treeParentCode} = 0
      }
#end
      this.formType = 1
      this.open = true
      this.formTitle = '添加'
    },
#if($table.sub)
    /** 新增子项操作 */
    handleAdd${subclassName} (row) {
      this.subList.push(row)
    },
    /** 删除子项操作 */
    handleDelete${subclassName} (index, indexs) {
      if (index !== undefined) {
        this.subList.splice(index, 1)
        return
      }
      indexs.forEach(item => {
        this.subList.splice(item, 1)
      })
    },
#end    
    /** 修改按钮操作 */
    handleUpdate (row, ids) {
      this.reset()
#if($tplCategory == 'tree')
      this.$emit('select-tree')
#end
      this.formType = 2
      const ${pkColumn.javaField} = row ? row.${pkColumn.javaField} : ids
      get${BusinessName}(${pkColumn.javaField}).then(response => {
        this.form = response.data
#if($table.sub)
        this.subList = response.data.${subclassName}List
#end
#foreach ($column in $columns)
#if($column.htmlType == "checkbox")
        if (this.form.$column.javaField !== null) {
          this.form.$column.javaField = this.form.${column.javaField}.split(',')
        } else {
          this.form.$column.javaField = undefined
        }
#end
#end
        this.open = true
        this.formTitle = '修改'
      })
    },
    /** 提交按钮 */
    submitForm: function () {
      this.#[[$]]#refs.form.validate(valid => {
        if (valid) {
          this.submitLoading = true
#foreach ($column in $columns)
#if($column.htmlType == "checkbox")
          this.form.$column.javaField = this.form.${column.javaField}.join(',')
#end
#end
#if($table.sub)
          this.form.${subclassName}List = this.subList
#end
          if (this.form.${pkColumn.javaField} !== undefined && this.form.${pkColumn.javaField} !== null) {
            update${BusinessName}(this.form).then(response => {
              this.$message.success(
                '修改成功',
                3
              )
              this.open = false
              this.$emit('ok')
            }).finally(() => {
              this.submitLoading = false
            })
          } else {
            add${BusinessName}(this.form).then(response => {
              this.$message.success(
                '新增成功',
                3
              )
              this.open = false
              this.$emit('ok')
            }).finally(() => {
              this.submitLoading = false
            })
          }
        } else {
          return false
        }
      })
    }
  }
}
</script>

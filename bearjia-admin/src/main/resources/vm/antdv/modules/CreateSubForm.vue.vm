<template>
  <!-- 增加修改 -->
  <a-drawer width="35%" :label-col="4" :wrapper-col="14" :visible="open" @close="onClose">
    <a-divider orientation="left">
      <b>${subTable.functionName}</b>
    </a-divider>
    <a-form-model ref="form" :model="form">
#set($columnLastIndex = 0)
#foreach($column in $subTable.columns)
#set($javaField=$column.javaField)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($column.pk || $javaField == ${subTableFkclassName})
#elseif($column.list && "" != $javaField)
#set($columnLastIndex = $foreach.count)
      <a-form-model-item label="${comment}" prop="${javaField}">
        <a-input v-model="form.${javaField}" placeholder="请输入" />
      </a-form-model-item>
#end
#end
      <div class="bottom-control">
        <a-space>
          <a-button type="primary" @click="submitForm">
            保存
          </a-button>
          <a-button type="dashed" @click="cancel">
            取消
          </a-button>
        </a-space>
      </div>
    </a-form-model>
  </a-drawer>
</template>

<script>

export default {
  name: 'CreateSubForm',
  props: {
  },
  components: {
  },
  data () {
    return {
      formTitle: '增加',
      // 表单参数
      form: {
#foreach($column in $subTable.columns)
#set($javaField=$column.javaField)
#if($column.pk || $javaField == ${subTableFkclassName})
#elseif($column.list && "" != $javaField)
        ${$javaField}: undefined#if($columnLastIndex != $foreach.count),#end

#end
#end
      },
      open: false
    }
  },
  filters: {
  },
  created () {
  },
  computed: {
  },
  watch: {
  },
  methods: {
    onClose () {
      this.open = false
    },
    // 取消按钮
    cancel () {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset () {
      this.form = {
#foreach($column in $subTable.columns)
#set($javaField=$column.javaField)
#if($column.pk || $javaField == ${subTableFkclassName})
#elseif($column.list && "" != $javaField)
        ${$javaField}: undefined#if($columnLastIndex != $foreach.count),#end

#end
#end
      }
    },
     /** 新增按钮操作 */
    handleAdd () {
      this.open = true
      this.reset()
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$emit('add', this.form)
      this.open = false
    }
  }
}
</script>
